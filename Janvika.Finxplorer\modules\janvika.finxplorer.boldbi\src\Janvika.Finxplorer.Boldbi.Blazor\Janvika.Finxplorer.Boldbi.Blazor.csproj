<Project Sdk="Microsoft.NET.Sdk.Razor">
  <Import Project="..\..\common.props" />
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Volo.Abp.AspNetCore.Mvc" Version="9.1.0" />
    <PackageReference Include="Volo.Abp.AutoMapper" Version="9.1.0" />
    <PackageReference Include="Volo.Abp.AspNetCore.Components.Web.Theming" Version="9.1.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Janvika.Finxplorer.Boldbi.Application.Contracts\Janvika.Finxplorer.Boldbi.Application.Contracts.csproj" />
    <ProjectReference Include="../../../janvika.finxplorer.quiltt/src/Janvika.Finxplorer.Quiltt.Blazor/Janvika.Finxplorer.Quiltt.Blazor.csproj" />
    <ProjectReference Include="../../../janvika.finxplorer.quiltt/src/Janvika.Finxplorer.Quiltt.Application.Contracts/Janvika.Finxplorer.Quiltt.Application.Contracts.csproj" />
    <ProjectReference Include="../../../janvika.finxplorer.quiltt/src/Janvika.Finxplorer.Quiltt.Application/Janvika.Finxplorer.Quiltt.Application.csproj" />
  </ItemGroup>
</Project>