using System;
using System.Linq;
using System.Threading.Tasks;
using Janvika.Finxplorer.Boldbi.Application.Contracts.Setup;
using Janvika.Finxplorer.Boldbi.Application.Services.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Volo.Abp;

namespace Janvika.Finxplorer.Boldbi.Application.Setup;

public class TenantAppService : BoldbiAppService, ITenantAppService
{
    private readonly EmbedProperties _embedProperties;

    private readonly IHttpService<
        TenantResponseDto,
        CreateTenantInputDto,
        object,
        TenantListResponseDto,
        object,
        CreateTenantApiResponseDto
    > _tenantHttpService;

    private readonly ILogger<TenantAppService> _logger;

    private readonly TenantListAppService _tenantListAppService;

    public TenantAppService(
        IOptions<EmbedProperties> embedPropertiesOptions,
        TenantListAppService tenantListAppService,
        ILogger<TenantAppService> logger,
        IHttpService<
            TenantResponseDto,
            CreateTenantInputDto,
            object,
            TenantListResponseDto,
            object,
            CreateTenantApiResponseDto
        > tenantHttpService
    )
    {
        _embedProperties = embedPropertiesOptions.Value;
        _logger = logger;
        _tenantHttpService = tenantHttpService;
        _tenantListAppService = tenantListAppService;
    }

    private async Task GetTenantTokenAsync()
    {
        _logger.LogInformation(
            "{0}:{1}:Getting tenant token.",
            nameof(TenantAppService),
            nameof(GetTenantTokenAsync)
        );
        try
        {
            var tokenRequest = TokenRequest.CreateClientCredentialsGrant(
                _embedProperties.BoldBITenantClientId,
                _embedProperties.BoldBITenantClientSecret
            );

            string tokenUrl = $"/api/token";

            await _tenantHttpService.AuthenticateAsync(tokenRequest, tokenUrl);
        }
        catch (Exception ex)
        {
            throw new UserFriendlyException("Failed to get tenant token.", ex.Message);
        }
    }

    public async Task<TenantDto> GetOrCreateTenantAsync(BoldBiSetupJobArgs args)
    {
        try
        {
            await GetTenantTokenAsync();

            //prepapre tenant creation dto
            var createTenantDto = new CreateTenantInputDto
            {
                Email = args.Email,
                ServerConfiguration = new ServerConfigurationDto
                {
                    Database = new DatabaseDto
                    {
                        ServerName = _embedProperties.DatabaseServerName,
                        DatabaseName = $"{args.AbpTenantName}",
                        UserName = _embedProperties.DatabaseUserName,
                        Password = _embedProperties.DatabasePassword,
                        IsNewDatabase = false,
                        MaintenanceDatabase = "postgres",
                        Port = "5432",
                        ServerType = 4,
                        SslEnabled = false,
                        AdditionalParameters = "",
                    },

                    Storage = new StorageDto
                    {
                        StorageType = 0,
                        AzureBlob = new AzureBlobDto
                        {
                            AzureBlobStorageContainerName = $"{args.AbpTenantName}", //$"{args.Username}dedicatedtenantsa",
                            AzureBlobStorageUri = _embedProperties.AzureBlobStorageUri,
                            ConnectionString = "string",
                            ConnectionType = "https",
                            AccountName = _embedProperties.AzureBlobAccountName,
                            AccessKey = _embedProperties.AzureBlobAccessKey,
                        },
                    },
                    Site = new SiteDto
                    {
                        TenantName = $"{args.AbpTenantName}", // $"{args.Username}-dedicated-tenant",
                        TenantIdentifier = $"{args.AbpTenantName}", //$"{args.Username}-dedicated-tenant",
                        UseSiteIdentifier = true,
                        TenantType = 3,
                    },
                },

                DataStoreConfiguration = new DataStoreConfigurationDto
                {
                    ServerName = _embedProperties.DatabaseServerName,
                    DatabaseName = $"{args.AbpTenantName}", // $"{args.Username}-dedicated-tenant",
                    UserName = _embedProperties.DatabaseUserName,
                    Password = _embedProperties.DatabasePassword,
                    IsNewDatabase = false,
                    MaintenanceDatabase = "postgres",
                    Port = "5432",
                    ServerType = 4,
                    SslEnabled = false,
                    AdditionalParameters = "",
                },
            };

            //Get list of all tenants
            var existingTenantId = await _tenantListAppService.FindTenantByNameWithPaginationAsync(
                createTenantDto.ServerConfiguration.Site.TenantName
            );

            // check if tenant exists
            if (existingTenantId != null)
                return new TenantDto { TenantId = existingTenantId };

            // Create new tenant
            var result = await _tenantHttpService.CreateAsyncWithStatusResponse(
                "/api/v2.0/tenants",
                createTenantDto
            );

            if (result.ApiStatus && result.Status)
                return new TenantDto { TenantId = result.Data };

            throw new UserFriendlyException(result.StatusMessage);
        }
        catch (Exception ex)
        {
            throw new UserFriendlyException("Failed to create or retrieve tenant." + ex.Message);
        }
    }

    public async Task<TenantResponseDto> GetTenantAsync(string tenantId)
    {
        try
        {
            await GetTenantTokenAsync(); // This will set the auth header via AuthenticateAsync
            return await _tenantHttpService.GetAsync($"/api/v2.0/tenants/{tenantId}");
        }
        catch (Exception ex)
        {
            throw new UserFriendlyException("Failed to get tenant details." + ex.Message);
        }
    }
}
