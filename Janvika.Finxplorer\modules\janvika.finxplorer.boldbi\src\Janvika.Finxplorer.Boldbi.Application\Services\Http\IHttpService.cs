using System.Collections.Generic;
using System.Threading.Tasks;
using Janvika.Finxplorer.Boldbi.Application.Contracts.Setup;

namespace Janvika.Finxplorer.Boldbi.Application.Services.Http;

public interface IHttpService<T, TC, TU, TL, TD, TR>
    where T : class
    where TC : class
    where TU : class
    where TL : class
    where TR : class
{
    //Task<ListResultDto<T>> GetListAsync(string uri, TL? getListRequestDto = default);
    Task<T> GetAsync(string uri);
    Task<TL> GetListAsync(string uri);
    Task<T> CreateAsync(string uri, TC createInputDto);
    Task<TR> CreateAsyncWithStatusResponse(string uri, TC createInputDto);
    Task CreateManyAsync(string uri, IEnumerable<TC> createManyInputDto);
    Task<ListResultDto<T>> UpdateAsync(string uri, TU updateInputDto);
    Task DeleteAsync(string uri, TD id);
    Task AuthenticateAsync(TokenRequest tokenRequest, string tokenUrl);
    Task<bool> RefreshTokenIfNeededAsync();
    Task<T> GetIfExistsAsync(string uri);
}
