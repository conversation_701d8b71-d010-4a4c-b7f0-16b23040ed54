{"name": "Janvika.Finxplorer.Boldbi.Application", "hash": "", "contents": [{"namespace": "Janvika.Finxplorer.Boldbi", "dependsOnModules": [{"declaringAssemblyName": "Janvika.Finxplorer.Boldbi.Domain", "namespace": "Janvika.Finxplorer.Boldbi", "name": "BoldbiDomainModule"}, {"declaringAssemblyName": "Janvika.Finxplorer.Boldbi.Application.Contracts", "namespace": "Janvika.Finxplorer.Boldbi", "name": "BoldbiApplicationContractsModule"}, {"declaringAssemblyName": "Volo.Abp.Ddd.Application", "namespace": "Volo.Abp.Application", "name": "AbpDddApplicationModule"}, {"declaringAssemblyName": "Volo.Abp.AutoMapper", "namespace": "Volo.Abp.AutoMapper", "name": "AbpAutoMapperModule"}, {"declaringAssemblyName": "Volo.Abp.PermissionManagement.Domain.Shared", "namespace": "Volo.Abp.PermissionManagement", "name": "AbpPermissionManagementDomainSharedModule"}], "implementingInterfaces": [{"name": "IAbpModule", "namespace": "Volo.Abp.Modularity", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.Modularity.IAbpModule"}, {"name": "IOnPreApplicationInitialization", "namespace": "Volo.Abp.Modularity", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.Modularity.IOnPreApplicationInitialization"}, {"name": "IOnApplicationInitialization", "namespace": "Volo.Abp", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.IOnApplicationInitialization"}, {"name": "IOnPostApplicationInitialization", "namespace": "Volo.Abp.Modularity", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.Modularity.IOnPostApplicationInitialization"}, {"name": "IOnApplicationShutdown", "namespace": "Volo.Abp", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.IOnApplicationShutdown"}, {"name": "IPreConfigureServices", "namespace": "Volo.Abp.Modularity", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.Modularity.IPreConfigureServices"}, {"name": "IPostConfigureServices", "namespace": "Volo.Abp.Modularity", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.Modularity.IPostConfigureServices"}], "contentType": "abpModule", "name": "BoldbiApplicationModule", "summary": null}]}