using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Blazorise;
using Janvika.Finxplorer.Boldbi.Application.Contracts.Integrations;
using Janvika.Finxplorer.Boldbi.Application.Contracts.Permissions;
using Janvika.Finxplorer.Boldbi.Application.Contracts.Setup;
using Janvika.Finxplorer.Quiltt.AccountDetail;

using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;
using Volo.Abp.AspNetCore.Components.Messages;
using Volo.Abp.AspNetCore.Components.Web.Theming.PageToolbars;
using Volo.Abp.AspNetCore.Mvc.AntiForgery;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.BlazoriseUI;

namespace Janvika.Finxplorer.Boldbi.Blazor.Pages;

public partial class TenantAnalytics : IAsyncDisposable
{
    [Inject]
    public IPermissionChecker PermissionChecker { get; set; } = default!;

    [Inject]
    public IJSRuntime JSRuntime { get; set; } = default!;

    [Inject]
    public IConfiguration? appConfig { get; set; }

    [Inject]
    public IBoldBiWorkflowAppService? _boldBiWorkflow { get; set; }

    [Inject]
    public IAbpAntiForgeryManager _abpAntiForgeryManager { get; set; } = default!;

    [Inject]
    public ILogger<TenantAnalytics> _logger { get; set; } = default!;

    [Inject]
    public INonMasterDashboardAppService? _nonMasterSiteDashboardAppService { get; set; }

    [Inject]
    public IUiMessageService _uiMessageService { get; set; } = default!;

    [Inject]
    public IQuilttTransactionsService? _quilttTransactionsService { get; set; } = default!;

    private TransactionUpdateDto EditingTransaction { get; set; } = new();
    private Validations EditingTransactionValidations { get; set; } = new();
    private Guid EditingTransactionId { get; set; }
    private Modal CreateTransactionModal { get; set; } = new();
    private Modal EditTransactionModal { get; set; } = new();

    protected List<Volo.Abp.BlazoriseUI.BreadcrumbItem> BreadcrumbItems = new();

    protected DateTime StartDate { get; set; }
    protected DateTime EndDate { get; set; }
    protected bool HasAuditLoggingPermission { get; set; }

    private IJSObjectReference? BoldBiJsSDK = null;
    private string? defaultDashboardName = "1_";

    // Track the current dashboard
    protected string CurrentDashboardId { get; set; } = string.Empty;

    // Store dashboard configurations
    protected List<DashboardConfig> Dashboards { get; set; } = new();

    // Tab management for Blazorise tabs
    protected string SelectedTab { get; set; } = string.Empty;

    // Fullscreen state tracking
    protected bool IsFullscreen { get; set; } = false;

    // Add loading state properties
    protected bool IsLoading { get; set; } = true;
    protected int LoadingStep { get; set; } = 0;

    // Track dashboard switching for smooth UX
    protected bool IsDashboardSwitching { get; set; } = false;

    protected override async Task OnInitializedAsync()
    {
        IsLoading = true;
        LoadingStep = 1;
        await InvokeAsync(StateHasChanged);

        _logger.LogInformation("OnInitializedAsync: Starting initialization");
        
        try
        {
            // Initialize dashboard configurations (no JS interop here)
            await InitializeDashboardConfigs();
            LoadingStep = 2;
            await InvokeAsync(StateHasChanged);
            await Task.Delay(300); // Small delay to show progress
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during OnInitializedAsync");
            await _uiMessageService.Error("Failed to initialize page. Please try again.");
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            LoadingStep = 3;
            await InvokeAsync(StateHasChanged);
            
            await InitBoldBiSDK();
            await TriggerInitialDashboardRender();
            
            // Hide loading after everything is ready
            IsLoading = false;
            await InvokeAsync(StateHasChanged);
        }
    }

    // New method to handle initial dashboard rendering after data is loaded
    private async Task TriggerInitialDashboardRender()
    {
        try
        {
            _logger.LogInformation("TriggerInitialDashboardRender: Attempting to render initial dashboard");

            // Wait a bit for the UI to update and ensure JS SDK is ready
            await Task.Delay(300);

            if (BoldBiJsSDK != null && !string.IsNullOrEmpty(CurrentDashboardId))
            {
                _logger.LogInformation("TriggerInitialDashboardRender: Rendering dashboard with ID: {DashboardId}, SelectedTab: {SelectedTab}", CurrentDashboardId, SelectedTab);

                // Force render the initial dashboard
                await renderDashboard(CurrentDashboardId);

                // Force UI update
                await InvokeAsync(StateHasChanged);
                _logger.LogInformation("TriggerInitialDashboardRender: Dashboard render completed and UI updated");
            }
            else
            {
                _logger.LogWarning("TriggerInitialDashboardRender: Cannot render - BoldBiJsSDK: {SDK}, CurrentDashboardId: {DashboardId}",
                    BoldBiJsSDK != null ? "Initialized" : "Null",
                    string.IsNullOrEmpty(CurrentDashboardId) ? "Empty" : CurrentDashboardId);

                // If JS SDK is not ready yet, try again
                if (BoldBiJsSDK == null && !string.IsNullOrEmpty(CurrentDashboardId))
                {
                    _logger.LogInformation("TriggerInitialDashboardRender: JS SDK not ready, retrying in 500ms");
                    await Task.Delay(500);
                    await InitBoldBiSDK();

                    if (BoldBiJsSDK != null)
                    {
                        await renderDashboard(CurrentDashboardId);
                        await InvokeAsync(StateHasChanged);
                        _logger.LogInformation("TriggerInitialDashboardRender: Retry successful");
                    }
                    else
                    {
                        _logger.LogError("TriggerInitialDashboardRender: Failed to initialize SDK on retry");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in TriggerInitialDashboardRender");
            await _uiMessageService.Error("Failed to load initial dashboard. Please try refreshing the page.");
        }
    }

    private async Task InitializeDashboardConfigs()
    {
        try
        {
            string? AbpTenantName = CurrentTenant?.Name;
            string? Username = CurrentUser?.Email; // Email is used as username.

            if (string.IsNullOrEmpty(AbpTenantName) || string.IsNullOrEmpty(Username) || _nonMasterSiteDashboardAppService == null)
            {
                _logger.LogWarning("Required information not available. Cannot initialize dashboards.");
                return;
            }

            _logger.LogInformation("Initializing dashboards for tenant: {TenantName}, user: {Username}", AbpTenantName, Username);

            // Get all dashboards for the user and tenant
            var allDashboards = await _nonMasterSiteDashboardAppService.ListDashboards(Username, AbpTenantName);

            if (allDashboards == null || !allDashboards.Any())
            {
                _logger.LogWarning("No dashboards found for tenant: {TenantName}, user: {Username}", AbpTenantName, Username);
                await _uiMessageService.Warn("No dashboards available for your account.");
                return;
            }

            // Dictionary to map category prefixes to icon classes
            var categoryIconMap = new Dictionary<string, string>
            {
                { "1_", "bi bi-graph-up" },      // overview
                { "2_", "bi bi-credit-card" },   // spend 
                { "3_", "bi bi-piggy-bank" },    // Budget
                { "4_", "bi bi-table" }          // advanced analysis. transaction pivot 
            };

            // Clear existing dashboard list
            Dashboards.Clear();

            // Process dashboards in order of categories (1_, 2_, 3_, 4_)
            foreach (var categoryPrefix in new[] { "1_", "2_", "3_", "4_" })
            {
                var dashboardsInCategory = allDashboards
                    .Where(d =>
                        d.Name.StartsWith(categoryPrefix, StringComparison.OrdinalIgnoreCase) ||
                        d.CategoryName.StartsWith(categoryPrefix, StringComparison.OrdinalIgnoreCase))
                    .OrderBy(d => d.Name);

                foreach (var dashboard in dashboardsInCategory)
                {
                    var dashboardName = dashboard.Name;

                    // Remove category prefix from name if present
                    if (dashboardName.StartsWith(categoryPrefix, StringComparison.OrdinalIgnoreCase))
                    {
                        dashboardName = dashboardName.Substring(categoryPrefix.Length).Trim();
                    }

                    Dashboards.Add(new DashboardConfig
                    {
                        Id = dashboard.Id,
                        Name = dashboardName,
                        IconClass = categoryIconMap.TryGetValue(categoryPrefix, out var iconClass) ? iconClass : "bi bi-clipboard-data"
                    });
                }
            }

            // Set the default dashboard - prioritize "1_" category (Finance Overview)
            if (Dashboards.Any())
            {
                // Try to find a dashboard that starts with "1_" first (Finance Overview)
                var defaultDashboard = allDashboards
                    .Where(d => d.Name.StartsWith("1_", StringComparison.OrdinalIgnoreCase) || 
                               d.CategoryName.StartsWith("1_", StringComparison.OrdinalIgnoreCase))
                    .OrderBy(d => d.Name)
                    .FirstOrDefault();

                if (defaultDashboard != null)
                {
                    CurrentDashboardId = defaultDashboard.Id;
                    SelectedTab = defaultDashboard.Id; // Set the selected tab
                    _logger.LogInformation("Set default dashboard (Finance Overview): {DashboardId} - {DashboardName}",
                        defaultDashboard.Id, defaultDashboard.Name);
                }
                else
                {
                    // Fallback to first available dashboard
                    CurrentDashboardId = Dashboards.First().Id;
                    SelectedTab = Dashboards.First().Id; // Set the selected tab
                    _logger.LogInformation("Set fallback default dashboard: {DashboardId} - {DashboardName}",
                        CurrentDashboardId, Dashboards.First().Name);
                }

                // Dashboard rendering will be triggered after SDK initialization in OnAfterRenderAsync
            }
            else
            {
                _logger.LogWarning("No dashboard configurations were created");
            }

            _logger.LogInformation("Dashboards initialized. Count: {Count}, Default ID: {DefaultId}", 
                Dashboards.Count, CurrentDashboardId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error initializing dashboard configurations");
            throw; // Re-throw to be handled by calling method
        }
    }

    private async Task InitBoldBiSDK()
    {
        if (BoldBiJsSDK != null)
        {
            _logger.LogInformation("BoldBI SDK already initialized");
            return;
        }

        try
        {
            _logger.LogInformation("Initializing BoldBI SDK");
            
            BoldBiJsSDK = await JSRuntime.InvokeAsync<IJSObjectReference>(
                "import",
                "./_content/Janvika.Finxplorer.BoldBi.Blazor/Pages/TenantAnalytics.razor.js"
            );

            if (BoldBiJsSDK != null)
            {
                // Set the .NET reference for JS interop callbacks
                await BoldBiJsSDK.InvokeVoidAsync("setDotNetReference", DotNetObjectReference.Create(this));
                _logger.LogInformation("BoldBI SDK initialized successfully with .NET reference");
            }
            else
            {
                _logger.LogError("BoldBI SDK initialization returned null");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize BoldBI SDK");
            throw; // Re-throw to be handled by calling method
        }
    }

    private async Task renderDashboard(string dashboardId)
    {
        if (string.IsNullOrEmpty(dashboardId))
        {
            _logger.LogWarning("Dashboard ID is null or empty");
            return;
        }

        if (BoldBiJsSDK == null)
        {
            _logger.LogError("BoldBiJsSDK is null when attempting to render dashboard");
            return;
        }

        if (appConfig == null)
        {
            _logger.LogError("AppConfig is null when attempting to render dashboard");
            return;
        }

        try
        {
            var serverUrl = appConfig["BoldBI:ServerUrl"]?.ToString();
            if (string.IsNullOrEmpty(serverUrl))
            {
                _logger.LogError("BoldBI ServerUrl configuration is missing");
                await _uiMessageService.Error("Dashboard configuration is missing. Please contact administrator.");
                return;
            }

            var boldBIConfig = new Dictionary<string, string>
            {
                ["ServerUrl"] = serverUrl,
                ["SiteIdentifier"] = "site/" + (CurrentTenant?.Name ?? string.Empty),
                ["DashboardId"] = dashboardId,
                ["AuthorizationServerBaseUrl"] = "/api/app/embed/get-details",
                ["RequestVerificationToken"] = _abpAntiForgeryManager.GenerateToken(),
            };

            _logger.LogInformation("Rendering dashboard with config - ServerUrl: {ServerUrl}, SiteIdentifier: {SiteIdentifier}, DashboardId: {DashboardId}",
                boldBIConfig["ServerUrl"], boldBIConfig["SiteIdentifier"], dashboardId);

            // Only cleanup if we're switching to a different dashboard
            if (CurrentDashboardId != dashboardId)
            {
                try
                {
                    await BoldBiJsSDK.InvokeVoidAsync("cleanupDashboard");
                    _logger.LogDebug("Dashboard cleanup completed for ID: {DashboardId}", dashboardId);
                }
                catch (JSException cleanupEx)
                {
                    // Cleanup failure is not critical, log and continue
                    _logger.LogWarning(cleanupEx, "Dashboard cleanup failed for ID: {DashboardId}, continuing with render", dashboardId);
                }

                // Small delay to ensure cleanup is complete and prevent rapid switching issues
                await Task.Delay(100);
            }

            await BoldBiJsSDK.InvokeVoidAsync("renderDashboard", boldBIConfig);

            _logger.LogInformation("Dashboard render command sent successfully for ID: {DashboardId}", dashboardId);
        }
        catch (JSException jsEx)
        {
            _logger.LogError(jsEx, "JavaScript error rendering dashboard: {DashboardId}. Message: {Message}", dashboardId, jsEx.Message);

            // Check for specific BoldBI errors that we can handle gracefully
            if (jsEx.Message.Contains("mode") || jsEx.Message.Contains("triggerDashboardRenderEvent") || jsEx.Message.Contains("null"))
            {
                _logger.LogWarning("Detected BoldBI context error for dashboard: {DashboardId}, this is usually harmless during tab switching", dashboardId);
                // Don't show error to user for these common switching issues
            }
            else
            {
                await _uiMessageService.Error($"Failed to render dashboard: {jsEx.Message}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error rendering dashboard: {DashboardId}", dashboardId);
            await _uiMessageService.Error("Failed to render dashboard. Please check the console for details.");
        }
    }

    private async Task<NonMasterDashboardItem?> GetDefaultDashboard(string AbpTenantName, string Username)
    {
        if (_nonMasterSiteDashboardAppService == null)
        {
            return null;
        }

        try
        {
            var dashboards = await _nonMasterSiteDashboardAppService.ListDashboards(Username, AbpTenantName);

            var dashboard = dashboards
                .Where(d =>
                    d.Name.Contains(defaultDashboardName ?? "", StringComparison.OrdinalIgnoreCase) ||
                    d.CategoryName.Contains(defaultDashboardName ?? "", StringComparison.OrdinalIgnoreCase))
                .OrderByDescending(d => d.ItemModifiedDate)
                .FirstOrDefault()
                ?? dashboards.OrderByDescending(d => d.ItemModifiedDate).FirstOrDefault();

            return dashboard;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting default dashboard");
            return null;
        }
    }

    protected virtual ValueTask SetBreadcrumbItemsAsync()
    {
        BreadcrumbItems.Add(new Volo.Abp.BlazoriseUI.BreadcrumbItem(L["Analytics"]));
        return ValueTask.CompletedTask;
    }

    protected virtual Task OnDatesChangedAsync(IReadOnlyList<DateTime> dates)
    {
        StartDate = dates.Min();
        EndDate = dates.Max();
        return Task.CompletedTask;
    }

    // Handle dashboard selection
    protected async Task RenderSelectedDashboard(string dashboardId)
    {
        if (string.IsNullOrEmpty(dashboardId))
        {
            return;
        }

        try
        {
            IsDashboardSwitching = true;
            await InvokeAsync(StateHasChanged);

            CurrentDashboardId = dashboardId;
            _logger.LogInformation("Switching to dashboard: {DashboardId}", dashboardId);

            // Render the selected dashboard
            await renderDashboard(dashboardId);

            IsDashboardSwitching = false;
            await InvokeAsync(StateHasChanged);
        }
        catch (Exception ex)
        {
            IsDashboardSwitching = false;
            await InvokeAsync(StateHasChanged);

            _logger.LogError(ex, "Error switching dashboard to: {DashboardId}", dashboardId);
            await _uiMessageService.Error("Failed to switch dashboard. Please try again.");
        }
    }

    // Handle tab selection change for Blazorise tabs
    protected async Task OnSelectedTabChanged(string tabName)
    {
        if (string.IsNullOrEmpty(tabName))
        {
            return;
        }

        try
        {
            SelectedTab = tabName;
            _logger.LogInformation("Tab changed to: {TabName}", tabName);

            // Add a small delay to prevent rapid switching issues and allow Blazor to update DOM
            await Task.Delay(150);

            // Trigger dashboard rendering for the selected tab
            await RenderSelectedDashboard(tabName);
        }
        catch (JSException jsEx)
        {
            _logger.LogError(jsEx, "JavaScript error handling tab change to: {TabName}. Message: {Message}", tabName, jsEx.Message);

            // Handle specific Blazor interop errors gracefully
            if (jsEx.Message.Contains("removeChild") || jsEx.Message.Contains("interop"))
            {
                _logger.LogWarning("Blazor interop error during tab switch, attempting recovery");
                // Don't show error to user, just log it
            }
            else
            {
                await _uiMessageService.Error("Failed to switch to selected tab. Please try again.");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling tab change to: {TabName}", tabName);
            await _uiMessageService.Error("Failed to switch to selected tab. Please try again.");
        }
    }

    // Transaction modal methods
    private async Task OpenEditTransactionModalAsync(TransactionDto input)
    {
        if (_quilttTransactionsService == null)
        {
            await _uiMessageService.Error("Transaction service not available");
            return;
        }

        try
        {
            var transaction = await _quilttTransactionsService.GetTransactionsById(input.Id);

            EditingTransactionId = transaction.Id;
            EditingTransaction = ObjectMapper.Map<TransactionDto, TransactionUpdateDto>(transaction);

            await EditingTransactionValidations.ClearAll();
            await EditTransactionModal.Show();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error opening edit transaction modal for ID: {TransactionId}", input.Id);
            await _uiMessageService.Error("Failed to load transaction details");
        }
    }

    private async Task UpdateTransactionAsync()
    {
        try
        {
            if (await EditingTransactionValidations.ValidateAll() == false)
            {
                return;
            }

            // TODO: Implement transaction update
            // await _quilttTransactionsService.UpdateAsync(EditingTransactionId, EditingTransaction);
            // await GetTransactionsAsync();
            
            await EditTransactionModal.Hide();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating transaction: {TransactionId}", EditingTransactionId);
            await _uiMessageService.Error("Failed to update transaction");
        }
    }

    private async Task CloseEditTransactionModalAsync()
    {
        try
        {
            await EditTransactionModal.Hide();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error closing edit transaction modal");
        }
    }

    [JSInvokable]
    public async Task HandleWidgetTransactionSelection(string transactionId)
    {
        if (_quilttTransactionsService == null)
        {
            _logger.LogError("Transaction service not available");
            await _uiMessageService.Error("Transaction service not available");
            return;
        }

        try
        {
            if (!Guid.TryParse(transactionId, out Guid id))
            {
                _logger.LogWarning("Invalid transaction ID format: {TransactionId}", transactionId);
                await _uiMessageService.Error("Invalid transaction ID format");
                return;
            }

            var transaction = await _quilttTransactionsService.GetTransactionsById(id);
            
            if (transaction == null)
            {
                _logger.LogWarning("No transaction found for ID: {TransactionId}", transactionId);
                await _uiMessageService.Error("Transaction not found");
                return;
            }

            EditingTransactionId = transaction.Id;
            EditingTransaction = ObjectMapper.Map<TransactionDto, TransactionUpdateDto>(transaction);
            
            // Check if components are initialized before using them
            if (EditingTransactionValidations != null)
            {
                try
                {
                    await EditingTransactionValidations.ClearAll();
                }
                catch (InvalidOperationException ex)
                {
                    _logger.LogWarning(ex, "Validation component not fully initialized");
                }
            }
            
            if (EditTransactionModal != null)
            {
                await EditTransactionModal.Show();
                await InvokeAsync(StateHasChanged);
            }
        }
        catch (Volo.Abp.Domain.Entities.EntityNotFoundException ex)
        {
            _logger.LogWarning(ex, "Transaction with ID {TransactionId} not found", transactionId);
            await _uiMessageService.Error("Transaction not found");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling widget transaction selection for ID: {TransactionId}", transactionId);
            await _uiMessageService.Error("Failed to load transaction details");
        }
    }



    protected async Task CheckDashboardState()
    {
        if (BoldBiJsSDK != null)
        {
            try
            {
                await BoldBiJsSDK.InvokeVoidAsync("checkDashboardState");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking dashboard state");
            }
        }
    }

    // Toggle fullscreen mode
    protected async Task ToggleFullscreen()
    {
        try
        {
            if (BoldBiJsSDK == null)
            {
                _logger.LogWarning("BoldBI SDK not initialized when toggling fullscreen");
                return;
            }

            if (!IsFullscreen)
            {
                // Enter fullscreen
                var success = await BoldBiJsSDK.InvokeAsync<bool>("enterFullscreen");
                if (success)
                {
                    IsFullscreen = true;
                    _logger.LogInformation("Entered fullscreen mode");
                }
            }
            else
            {
                // Exit fullscreen
                var success = await BoldBiJsSDK.InvokeAsync<bool>("exitFullscreen");
                if (success)
                {
                    IsFullscreen = false;
                    _logger.LogInformation("Exited fullscreen mode");
                }
            }

            // Update UI
            await InvokeAsync(StateHasChanged);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error toggling fullscreen mode");
            await _uiMessageService.Error("Failed to toggle fullscreen mode");
        }
    }

    // Handle fullscreen exit from JavaScript (when user presses Escape)
    [JSInvokable]
    public async Task OnFullscreenExited()
    {
        try
        {
            IsFullscreen = false;
            await InvokeAsync(StateHasChanged);
            _logger.LogInformation("Fullscreen exited via JavaScript");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling fullscreen exit from JavaScript");
        }
    }

    // Implement IAsyncDisposable
    public async ValueTask DisposeAsync()
    {
        try
        {
            if (BoldBiJsSDK != null)
            {
                // Exit fullscreen if active
                if (IsFullscreen)
                {
                    await BoldBiJsSDK.InvokeVoidAsync("exitFullscreen");
                }
                
                await BoldBiJsSDK.DisposeAsync();
                BoldBiJsSDK = null;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disposing TenantAnalytics component");
        }
    }
}

// Dashboard configuration class
public class DashboardConfig
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string IconClass { get; set; } = string.Empty;
}
