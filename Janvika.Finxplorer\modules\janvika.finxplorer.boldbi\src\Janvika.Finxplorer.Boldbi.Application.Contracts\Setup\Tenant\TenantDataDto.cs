using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Janvika.Finxplorer.Boldbi.Application.Contracts.Setup;

public class TenantData
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("tenant_name")]
    public string TenantName { get; set; }

    [JsonProperty("url")]
    public string Url { get; set; }

    [JsonProperty("tenant_type")]
    public string TenantType { get; set; }

    [JsonProperty("created_date")]
    public DateTime CreatedDate { get; set; }

    [JsonProperty("modified_date")]
    public DateTime ModifiedDate { get; set; }

    [JsonProperty("tenant_status")]
    public string TenantStatus { get; set; }

    [JsonProperty("use_site_identifier")]
    public bool UseSiteIdentifier { get; set; }

    [JsonProperty("is_master")]
    public bool IsMaster { get; set; }

    [JsonProperty("client_secret")]
    public string ClientSecret { get; set; }

    [JsonProperty("schema_name")]
    public string SchemaName { get; set; }

    [JsonProperty("prefix")]
    public string Prefix { get; set; }
}

public class LinkDto
{
    [JsonProperty("link")]
    public string Link { get; set; }

    [JsonProperty("rel")]
    public string Rel { get; set; }

    [JsonProperty("type")]
    public string Type { get; set; }
}
