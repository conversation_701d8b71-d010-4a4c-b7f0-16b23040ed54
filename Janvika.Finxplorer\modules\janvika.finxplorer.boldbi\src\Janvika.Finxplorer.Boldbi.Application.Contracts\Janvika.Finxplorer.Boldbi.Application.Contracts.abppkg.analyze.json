{"name": "Janvika.Finxplorer.Boldbi.Application.Contracts", "hash": "", "contents": [{"namespace": "Janvika.Finxplorer.Boldbi", "dependsOnModules": [{"declaringAssemblyName": "Janvika.Finxplorer.Boldbi.Domain.Shared", "namespace": "Janvika.Finxplorer.Boldbi", "name": "BoldbiDomainSharedModule"}, {"declaringAssemblyName": "Volo.Abp.Ddd.Application.Contracts", "namespace": "Volo.Abp.Application", "name": "AbpDddApplicationContractsModule"}, {"declaringAssemblyName": "Volo.Abp.Authorization.Abstractions", "namespace": "Volo.Abp.Authorization", "name": "AbpAuthorizationAbstractionsModule"}], "implementingInterfaces": [{"name": "IAbpModule", "namespace": "Volo.Abp.Modularity", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.Modularity.IAbpModule"}, {"name": "IOnPreApplicationInitialization", "namespace": "Volo.Abp.Modularity", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.Modularity.IOnPreApplicationInitialization"}, {"name": "IOnApplicationInitialization", "namespace": "Volo.Abp", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.IOnApplicationInitialization"}, {"name": "IOnPostApplicationInitialization", "namespace": "Volo.Abp.Modularity", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.Modularity.IOnPostApplicationInitialization"}, {"name": "IOnApplicationShutdown", "namespace": "Volo.Abp", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.IOnApplicationShutdown"}, {"name": "IPreConfigureServices", "namespace": "Volo.Abp.Modularity", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.Modularity.IPreConfigureServices"}, {"name": "IPostConfigureServices", "namespace": "Volo.Abp.Modularity", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.Modularity.IPostConfigureServices"}], "contentType": "abpModule", "name": "BoldbiApplicationContractsModule", "summary": null}]}