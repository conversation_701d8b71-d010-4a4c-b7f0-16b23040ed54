{"name": "Janvika.Finxplorer.Boldbi.Domain.Shared", "hash": "", "contents": [{"namespace": "Janvika.Finxplorer.Boldbi", "dependsOnModules": [{"declaringAssemblyName": "Volo.Abp.Validation", "namespace": "Volo.Abp.Validation", "name": "AbpValidationModule"}, {"declaringAssemblyName": "Volo.Abp.Features", "namespace": "Volo.Abp.Features", "name": "AbpFeaturesModule"}], "implementingInterfaces": [{"name": "IAbpModule", "namespace": "Volo.Abp.Modularity", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.Modularity.IAbpModule"}, {"name": "IOnPreApplicationInitialization", "namespace": "Volo.Abp.Modularity", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.Modularity.IOnPreApplicationInitialization"}, {"name": "IOnApplicationInitialization", "namespace": "Volo.Abp", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.IOnApplicationInitialization"}, {"name": "IOnPostApplicationInitialization", "namespace": "Volo.Abp.Modularity", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.Modularity.IOnPostApplicationInitialization"}, {"name": "IOnApplicationShutdown", "namespace": "Volo.Abp", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.IOnApplicationShutdown"}, {"name": "IPreConfigureServices", "namespace": "Volo.Abp.Modularity", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.Modularity.IPreConfigureServices"}, {"name": "IPostConfigureServices", "namespace": "Volo.Abp.Modularity", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.Modularity.IPostConfigureServices"}], "contentType": "abpModule", "name": "BoldbiDomainSharedModule", "summary": null}]}