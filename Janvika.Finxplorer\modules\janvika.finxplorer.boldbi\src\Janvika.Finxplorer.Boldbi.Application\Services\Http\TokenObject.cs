// using System;
// using System.Collections.Generic;
// using System.Net;
// using System.Net.Http;
// using System.Text;
// using System.Threading.Tasks;
// using Newtonsoft.Json;

// namespace Janvika.Finxplorer.Boldbi.Application.Services.Http;

// public class TokenObject
// {
//     [JsonProperty("access_token")]
//     public string AccessToken { get; set; }

//     [JsonProperty("token_type")]
//     public string TokenType { get; set; }

//     [JsonProperty("expires_in")]
//     public int ExpiresIn { get; set; }

//     public DateTime ExpiresAt { get; set; }
// }
