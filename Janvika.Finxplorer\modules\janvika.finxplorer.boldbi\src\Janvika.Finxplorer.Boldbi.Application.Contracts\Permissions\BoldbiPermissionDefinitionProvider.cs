﻿using Janvika.Finxplorer.Boldbi.Localization;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Localization;

namespace Janvika.Finxplorer.Boldbi.Application.Contracts.Permissions;

public class BoldbiPermissionDefinitionProvider : PermissionDefinitionProvider
{
    public override void Define(IPermissionDefinitionContext context)
    {
        var boldBiPermissionsGroup = context.AddGroup(
            BoldbiPermissions.GroupName,
            L("Permission:Boldbi")
        );

        boldBiPermissionsGroup.AddPermission(
            BoldbiPermissions.Tenant.Default,
            L("Permission:Tenant")
        );

        boldBiPermissionsGroup.AddPermission(BoldbiPermissions.<PERSON>.<PERSON>, L("Permission:Host"));
    }

    private static LocalizableString L(string name)
    {
        return LocalizableString.Create<BoldbiResource>(name);
    }
}
