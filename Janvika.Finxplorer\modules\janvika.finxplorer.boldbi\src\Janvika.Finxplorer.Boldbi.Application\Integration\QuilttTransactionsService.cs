using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using Janvika.Finxplorer.Boldbi.Application;
using Janvika.Finxplorer.Boldbi.Application.Contracts.Integrations;
using Janvika.Finxplorer.Boldbi.Application.Contracts.Setup;
using Janvika.Finxplorer.Quiltt.AccountDetail;
using Janvika.Finxplorer.Boldbi.Application.Services.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Volo.Abp;

namespace Janvika.Finxplorer.BoldBI.Application.Integration;

public class QuilttTransactionsService : BoldbiAppService, IQuilttTransactionsService

{
   
   private readonly ITransactionsIntegrationService _transactionsIntegrationService;
 

    public QuilttTransactionsService(   
        ITransactionsIntegrationService transactionsIntegrationService
    )
    {
       _transactionsIntegrationService = transactionsIntegrationService;
    }

 
    public async Task<TransactionDto> GetTransactionsById(Guid itemId)
    {

        var transaction = await _transactionsIntegrationService.GetAsync(itemId);
        return transaction;
    }

     public async Task<TransactionDto> GetTransactionsByTransactionId(string transactionId)
    {

        var transaction = await _transactionsIntegrationService.GetByTransactionIdAsync(transactionId);
        return transaction;
    }
}
