// SandDance Visualization
export async function initializeSandDance(dotnetRef) {
  const embedIframe = document.getElementById("sandDanceIframe");
  if (!embedIframe) {
    console.error("SandDance iframe not found");
    await dotnetRef.invokeMethodAsync(
      "OnSandDanceError",
      "SandDance iframe not found"
    );
    return;
  }

  const transactionInsight = {
    chart: "barchartV",
    columns: {
      x: "Institution",
      y: "Amount",
      color: "Category",
    },
    scheme: "category10",
    view: "2d",
  };

  const explorerProps = {
    initialSidebarClosed: true,
    initialRenderer: { advanced: true },
  };

  // Set up iframe event handlers
  embedIframe.onload = async () => {
    try {
      //console.log("SandDance iframe loaded, fetching transaction data...");

      // Notify C# that iframe is loading
      await dotnetRef.invokeMethodAsync("OnSandDanceIframeLoading");

      // Small delay to ensure iframe is fully ready
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Notify C# that data loading is starting
      await dotnetRef.invokeMethodAsync("OnSandDanceDataLoading");

      // Get transaction data from C#
      const transactions = await dotnetRef.invokeMethodAsync(
        "GetTransactionsForVisualization"
      );

      // console.log(
      //   `Processing ${transactions.length} transactions for visualization`
      // );

      // Transform transaction data for SandDance
      const visualData = transactions.map((t) => ({
        "Transaction Date": new Date(t.transactionDate)
          .toISOString()
          .split("T")[0],
        Amount: Math.abs(parseFloat(t.amount)),
        "Sub Category":
          t.fingoal_enrichment_response_categorydescription || "Uncategorized",
        Category:
          t.fingoal_enrichment_response_highlevelcategorydescription ||
          "Uncategorized",
        "Detailed Sub Category":
          t.fingoal_enrichment_response_detailcategorydescription ||
          "Uncategorized",
        Merchant: t.transaction_merchant_name || "Unknown",
        "Transaction Type": t.transaction_entrytype || "Unknown",
        "Transaction Kind": t.transaction_kind || "Unknown",
        Description:
          t.fingoal_enrichment_response_simpledescription || "Unknown",
        Institution: t.institutionName || "Unknown",
        Account: t.accountName || "Unknown",
      }));

      //console.log("Sending data to SandDance iframe...");

      // Send data to SandDance iframe
      embedIframe.contentWindow.postMessage(
        {
          action: "load",
          data: visualData,
          insight: transactionInsight,
          props: explorerProps,
        },
        "*"
      );

      console.log("Data sent to SandDance, starting completion timer...");

      // Immediate completion after sending data (most reliable approach)
      // Since SandDance iframe communication is unreliable, we'll complete after data is sent
      setTimeout(async () => {
        console.log("Completing SandDance loading (1 second after data sent)");
        await dotnetRef.invokeMethodAsync("OnSandDanceLoadComplete");
      }, 1000); // Complete 1 second after sending data

      // Optional: Still listen for SandDance messages but don't depend on them
      const messageHandler = (event) => {
        if (event.source === embedIframe.contentWindow) {
          if (event.data && event.data.action === "loaded") {
            console.log("SandDance sent completion message");
          } else if (event.data && event.data.action === "error") {
            console.error("SandDance error:", event.data.message);
          }
        }
      };

      window.addEventListener("message", messageHandler);

      // Clean up listener after 10 seconds
      setTimeout(() => {
        window.removeEventListener("message", messageHandler);
      }, 10000);
    } catch (error) {
      console.error("Error loading transaction data:", error);
      await dotnetRef.invokeMethodAsync(
        "OnSandDanceError",
        error.message || "Failed to load transaction data"
      );
    }
  };

  // Handle iframe loading errors
  embedIframe.onerror = async (error) => {
    console.error("SandDance iframe error:", error);
    await dotnetRef.invokeMethodAsync(
      "OnSandDanceError",
      "Failed to load SandDance iframe"
    );
  };

  // If iframe is already loaded, trigger onload manually
  if (
    embedIframe.contentDocument &&
    embedIframe.contentDocument.readyState === "complete"
  ) {
    setTimeout(() => embedIframe.onload(), 100);
  }
}

export function changeSandDanceView(viewType) {
  const embedIframe = document.getElementById("sandDanceIframe");
  if (!embedIframe) {
    console.error("SandDance iframe not found for view change");
    return;
  }

  const insights = {
    bar: {
      chart: "barchartV",
      columns: {
        x: "Category",
        y: "Amount",
        color: "Category",
      },
      view: "2d",
    },
    scatter: {
      chart: "scatterplot",
      columns: {
        x: "Transaction Date",
        y: "Amount",
        z: "Category",
        color: "Institution",
      },
      view: "3d",
    },
    treemap: {
      chart: "treemap",
      columns: {
        size: "Amount",
        color: "Category",
        group: ["Institution", "Category"],
      },
      view: "2d",
    },
  };

  if (insights[viewType]) {
    embedIframe.contentWindow.postMessage(
      {
        action: "load",
        insight: insights[viewType],
      },
      "*"
    );
  }
}

export function toggleSandDanceTheme(isDark) {
  const embedIframe = document.getElementById("sandDanceIframe");
  if (!embedIframe) {
    console.error("SandDance iframe not found for theme toggle");
    return;
  }

  embedIframe.contentWindow.postMessage(
    {
      action: "theme",
      dark: isDark,
    },
    "*"
  );
}
