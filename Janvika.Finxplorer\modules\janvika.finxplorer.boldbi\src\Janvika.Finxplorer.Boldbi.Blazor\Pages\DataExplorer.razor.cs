using System;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using System.Web;
using System.Collections.Generic;
using Blazorise;
using Blazorise.DataGrid;
using Janvika.Finxplorer.Boldbi;
using Janvika.Finxplorer.Boldbi.Application.Contracts.Permissions;
using Janvika.Finxplorer.Boldbi.Application.Contracts.Setup;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.JSInterop;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.AspNetCore.Components.Web.Theming.Layout;
using Volo.Abp.AspNetCore.Components.Messages;
using Volo.Abp.AspNetCore.Components.Progression;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.EventBus.Distributed;
using Volo.Abp.MultiTenancy;
using Janvika.Finxplorer.Quiltt.AccountDetail;

namespace Janvika.Finxplorer.Boldbi.Blazor.Pages
{
    public partial class DataExplorer : IAsyncDisposable
    {
        [Inject]
        private IConfiguration Configuration { get; set; } = default!;

        protected List<Volo.Abp.BlazoriseUI.BreadcrumbItem> BreadcrumbItems =
            new List<Volo.Abp.BlazoriseUI.BreadcrumbItem>();

        private IJSObjectReference? module;
        private DotNetObjectReference<DataExplorer> objRef = default!;

        [Inject]
        private ILogger<DataExplorer> _logger { get; set; } = default!;

        [Inject]
        public ITransactionsAppService _transactionsAppService { get; set; } = default!;

        [Inject]
        private IUiPageProgressService PageProgressService { get; set; } = default!;

        [Inject]
        private IUiMessageService _UiMessageService { get; set; } = default!;

        [Inject]
        private IBackgroundJobManager _backgroundJobManager { get; set; } = default!;

        private ICurrentTenant _currentTenant { get; set; } = default!;

        [Inject]
        private IJSRuntime JS { get; set; } = default!;

        // Track loading state
        private bool _isLoading = false;
        private bool _isDarkTheme = false;

        protected override async Task OnInitializedAsync()
        {
            // Data initialization if needed
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                await SetBreadcrumbItemsAsync();

                try
                {
                    _logger.LogInformation("Starting DataExplorer initialization");
                    
                    // Start loading indicator
                    _isLoading = true;
                    await StartLoadingIndicator();

                    // Initialize JS module reference
                    module = await JS.InvokeAsync<IJSObjectReference>(
                        "import",
                        "./_content/Janvika.Finxplorer.BoldBi.Blazor/Pages/DataExplorer.razor.js"
                    );

                    // Create DotNetObjectReference for JS interop
                    objRef = DotNetObjectReference.Create(this);

                    // Initialize SandDance visualization - this will handle its own completion
                    await InitializeSandDanceVisualization();

                    _logger.LogInformation("DataExplorer JS module loaded, SandDance initialization started");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error loading DataExplorer module");
                    await StopLoadingIndicator();
                    await _UiMessageService.Error("Failed to load data visualization. Please refresh the page.");
                }

                await InvokeAsync(StateHasChanged);
            }
        }

        protected virtual ValueTask SetBreadcrumbItemsAsync()
        {
            BreadcrumbItems.Add(new Volo.Abp.BlazoriseUI.BreadcrumbItem(L["DataExplorer"]));
            return ValueTask.CompletedTask;
        }

        // Loading indicator methods
        private async Task StartLoadingIndicator()
        {
            try
            {
                await PageProgressService.Go(25, options =>
                {
                    options.Type = UiPageProgressType.Warning;
                });
                
                _logger.LogInformation("Loading indicator started");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error starting loading indicator");
            }
        }

        private async Task UpdateLoadingProgress(int percentage)
        {
            try
            {
                if (_isLoading)
                {
                    await PageProgressService.Go(percentage, options =>
                    {
                        options.Type = UiPageProgressType.Warning;
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating loading progress");
            }
        }

        private async Task StopLoadingIndicator()
        {
            try
            {
                _isLoading = false;
                await PageProgressService.Go(100);
                await Task.Delay(200); // Brief pause to show completion
                await PageProgressService.Go(-1); // Hide the indicator
                
                _logger.LogInformation("Loading indicator stopped");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error stopping loading indicator");
            }
        }

        // SandDance Integration Methods
        private async Task InitializeSandDanceVisualization()
        {
            try
            {
                if (module != null)
                {
                    _logger.LogInformation("Initializing SandDance visualization");
                    await UpdateLoadingProgress(50);
                    
                    await module.InvokeVoidAsync("initializeSandDance", objRef);
                    _logger.LogInformation("SandDance initialization command sent");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initializing SandDance visualization");
                await StopLoadingIndicator();
            }
        }

        // Called by JavaScript when iframe starts loading
        [JSInvokable]
        public async Task OnSandDanceIframeLoading()
        {
            try
            {
                _logger.LogInformation("SandDance iframe is loading");
                await UpdateLoadingProgress(60);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in OnSandDanceIframeLoading");
            }
        }

        // Called by JavaScript when data loading starts
        [JSInvokable]
        public async Task OnSandDanceDataLoading()
        {
            try
            {
                _logger.LogInformation("SandDance data loading started");
                await UpdateLoadingProgress(75);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in OnSandDanceDataLoading");
            }
        }

        // Called by JavaScript when everything is complete
        [JSInvokable]
        public async Task OnSandDanceLoadComplete()
        {
            try
            {
                _logger.LogInformation("SandDance visualization loaded successfully");
                await StopLoadingIndicator();
                await InvokeAsync(StateHasChanged);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in OnSandDanceLoadComplete");
            }
        }

        // Called by JavaScript if there's an error
        [JSInvokable]
        public async Task OnSandDanceError(string errorMessage)
        {
            try
            {
                _logger.LogError("SandDance error: {ErrorMessage}", errorMessage);
                await StopLoadingIndicator();
                await _UiMessageService.Error($"Visualization error: {errorMessage}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in OnSandDanceError");
            }
        }

        [JSInvokable]
        public async Task<List<TransactionConnectionDto>> GetTransactionsForVisualization()
        {
            try
            {
                _logger.LogInformation("Getting transactions for SandDance visualization");
                await UpdateLoadingProgress(80);
                
                // Fetch transactions using the service
                var result = await _transactionsAppService.GetTransactionConnectionListAsync();
                _logger.LogInformation($"Retrieved {result.Count} transactions for visualization");
                
                await UpdateLoadingProgress(90);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting transactions for visualization");
                await StopLoadingIndicator();
                await _UiMessageService.Error("Failed to load transaction data for visualization");
                return new List<TransactionConnectionDto>();
            }
        }

        public async Task ChangeSandDanceView(string viewType)
        {
            try
            {
                if (module != null)
                {
                    await module.InvokeVoidAsync("changeSandDanceView", viewType);
                    _logger.LogInformation($"Changed SandDance view to {viewType}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error changing SandDance view to {viewType}");
            }
        }

        public async Task ToggleTheme()
        {
            try
            {
                _isDarkTheme = !_isDarkTheme;
                if (module != null)
                {
                    await module.InvokeVoidAsync("toggleSandDanceTheme", _isDarkTheme);
                    _logger.LogInformation($"Toggled SandDance theme to {(_isDarkTheme ? "dark" : "light")}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error toggling SandDance theme");
            }
        }

        public async ValueTask DisposeAsync()
        {
            try 
            {
                _isLoading = false;
                
                if (objRef is not null)
                {
                    objRef.Dispose();
                }

                if (module is not null)
                {
                    await module.DisposeAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error disposing DataExplorer component");
            }
        }
    }
}