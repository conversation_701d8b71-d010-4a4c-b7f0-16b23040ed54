using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Janvika.Finxplorer.Boldbi.Application.Contracts.Setup;

public class PublishItemsResponseDto
{
    [JsonProperty("Data")]
    public List<PublishItemDto> Data { get; set; }

    [JsonProperty("TotalResults")]
    public int TotalResults { get; set; }

    [JsonProperty("Links")]
    public List<LinkInfo> Links { get; set; }
}

public class PublishItemDto
{
    [JsonProperty("PublishId")]
    public string PublishId { get; set; }

    [JsonProperty("ItemId")]
    public string ItemId { get; set; }

    [JsonProperty("TenantId")]
    public string TenantId { get; set; }

    [JsonProperty("LastSynchronizedStatus")]
    public string LastSynchronizedStatus { get; set; }

    [JsonProperty("LastSynchronizedOn")]
    public DateTime LastSynchronizedOn { get; set; }
}
