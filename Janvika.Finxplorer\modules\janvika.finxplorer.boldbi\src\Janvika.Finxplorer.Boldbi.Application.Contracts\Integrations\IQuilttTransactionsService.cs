

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Janvika.Finxplorer.Boldbi.Application.Contracts.Setup;
using Janvika.Finxplorer.Quiltt.AccountDetail;

namespace Janvika.Finxplorer.Boldbi.Application.Contracts.Integrations;
public interface IQuilttTransactionsService
{
        Task<TransactionDto>  GetTransactionsById(Guid Id);
        Task<TransactionDto>  GetTransactionsByTransactionId(string transactionId);
}


  