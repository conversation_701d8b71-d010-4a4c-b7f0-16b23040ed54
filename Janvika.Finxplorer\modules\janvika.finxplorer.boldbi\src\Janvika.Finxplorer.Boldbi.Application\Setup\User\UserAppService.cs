using System;
using System.Threading.Tasks;
using Janvika.Finxplorer.Boldbi.Application.Contracts.Setup;
using Janvika.Finxplorer.Boldbi.Application.Services.Http;
using Microsoft.Extensions.Options;
using Volo.Abp;

namespace Janvika.Finxplorer.Boldbi.Application.Setup;

public class BoldBiUserAppService : BoldbiAppService, IBoldBiUserAppService
{
    private readonly EmbedProperties _embedProperties;
    private readonly IHttpService<
        UserDto,
        CreateUserDto,
        object,
        object,
        object,
        object
    > _httpService;

    public BoldBiUserAppService(
        IOptions<EmbedProperties> embedPropertiesOptions,
        IHttpService<UserDto, CreateUserDto, object, object, object, object> httpService
    )
    {
        _embedProperties = embedPropertiesOptions.Value;
        _httpService = httpService;
    }

    public async Task GetAdminTokenAsync()
    {
        var tokenRequest = TokenRequest.CreatePasswordGrant(
            _embedProperties.UserEmail,
            _embedProperties.UserPassword
        );

        string masterSiteTokenUrl = $"/bi/api/{_embedProperties.SiteIdentifier}/token";

        await _httpService.AuthenticateAsync(tokenRequest, masterSiteTokenUrl);
    }

    public async Task<UserDto> GetorCreateUserAsync(BoldBiSetupJobArgs args)
    {
        //Input
        var userDto = new CreateUserDto
        {
            Username = args.Email, // email is used as username. it is guaranteed to be unique across the system
            Email = args.Email,
            FirstName = args.FirstName,
            LastName = args.LastName,
            Password = _embedProperties.StandardPassword,
        };

        //Authenticating with Admin Token
        await GetAdminTokenAsync(); // This will set the auth token in HttpService

        // Try to retrieve existing user.
        var existingUser = await _httpService.GetIfExistsAsync(
            $"/bi/api/{_embedProperties.SiteIdentifier}/v5.0/users/{userDto.Username}"
        );

        if (existingUser != null)
        {
            return existingUser;
        }

        // Create and return new user if not existing
        return await _httpService.CreateAsync(
            $"/bi/api/{_embedProperties.SiteIdentifier}/v5.0/users",
            userDto
        );
    }
}
