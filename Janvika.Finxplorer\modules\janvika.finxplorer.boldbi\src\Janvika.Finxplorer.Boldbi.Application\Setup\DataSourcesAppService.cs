using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using Janvika.Finxplorer.Boldbi.Application;
using Janvika.Finxplorer.Boldbi.Application.Contracts.Setup;
using Janvika.Finxplorer.Boldbi.Application.Services.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Volo.Abp;

namespace Janvika.Finxplorer.BoldBI.Application.Setup;

public class DataSourcesAppService : BoldbiAppService, IDataSourcesAppService
{
    private readonly EmbedProperties _embedProperties;
    private readonly IHttpService<
        DataSourceResponseDto,
        object,
        object,
        object,
        object,
        object
    > _dataSourceHttpService;
    private readonly ILogger<DataSourcesAppService> _logger;

    public DataSourcesAppService(
        IOptions<EmbedProperties> embedPropertiesOptions,
        ILogger<DataSourcesAppService> logger,
        IHttpService<
            DataSourceResponseDto,
            object,
            object,
            object,
            object,
            object
        > dashboardHttpService
    )
    {
        _embedProperties = embedPropertiesOptions.Value;
        _logger = logger;
        _dataSourceHttpService = dashboardHttpService;
    }

    public async Task GetAdminTokenAsync()
    {
        var tokenRequest = TokenRequest.CreatePasswordGrant(
            _embedProperties.UserEmail,
            _embedProperties.UserPassword
        );

        string masterSiteTokenUrl = $"/bi/api/{_embedProperties.SiteIdentifier}/token";

        await _dataSourceHttpService.AuthenticateAsync(tokenRequest, masterSiteTokenUrl);
    }

    public async Task<List<DataSourceResponseDetailDto>> GetDataSourcesAsync()
    {
        try
        {
            await GetAdminTokenAsync();

            const int pageSize = 25;
            int currentPage = 1;
            int totalPages = 1;
            var allDataSources = new List<DataSourceResponseDetailDto>();

            do
            {
                try
                {
                    var response = await _dataSourceHttpService.GetAsync(
                        $"/bi/api/{_embedProperties.SiteIdentifier}/v5.0/datasources?page={currentPage}&page_size={pageSize}"
                    );

                    if (response == null)
                    {
                        _logger.LogWarning("No data sources found on page {Page}", currentPage);
                        break;
                    }

                    allDataSources.AddRange(response.Data);

                    totalPages = (int)Math.Ceiling(response.TotalResults / (double)pageSize);

                    if (currentPage >= totalPages)
                    {
                        break;
                    }

                    currentPage++;
                }
                catch (HttpServiceException ex)
                {
                    _logger.LogError(
                        ex,
                        "Error retrieving data sources on page {Page}",
                        currentPage
                    );
                    throw new UserFriendlyException($"Failed to get data sources: {ex.Message}");
                }
            } while (currentPage <= totalPages);

            if (allDataSources.Any())
            {
                return allDataSources;
            }

            throw new UserFriendlyException("No data sources found.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get data sources");
            throw new UserFriendlyException($"Failed to get data sources: {ex.Message}");
        }
    }
}
