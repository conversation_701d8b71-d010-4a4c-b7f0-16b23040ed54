using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Janvika.Finxplorer.Boldbi.Application.Contracts.Setup;

// used for GET
public class CustomAttributeResponseDto
{
    [JsonProperty("ApiStatus")]
    public bool ApiStatus { get; set; }

    [JsonProperty("Data")]
    public CustomAttributeDto[] Data { get; set; }

    [JsonProperty("Status")]
    public bool Status { get; set; }

    [JsonProperty("StatusMessage")]
    public string StatusMessage { get; set; }
}

public class CustomAttributeDto
{
    [JsonProperty("Id")]
    public int Id { get; set; }

    [JsonProperty("Name")]
    public string Name { get; set; }

    [JsonProperty("Value")]
    public string Value { get; set; }

    [JsonProperty("Description")]
    public string Description { get; set; }

    [JsonProperty("CanEncrypt")]
    public bool CanEncrypt { get; set; }

    [JsonProperty("AttributeType")]
    public int AttributeType { get; set; }

    [JsonProperty("UserId")]
    public int UserId { get; set; }

    [JsonProperty("GroupId")]
    public int GroupId { get; set; }

    [JsonProperty("CreatedById")]
    public int CreatedById { get; set; }

    [JsonProperty("ModifiedById")]
    public int ModifiedById { get; set; }

    [JsonProperty("CreatedByIdPReferenceId")]
    public Guid CreatedByIdPReferenceId { get; set; }

    [JsonProperty("ModifiedByIdPReferenceId")]
    public Guid ModifiedByIdPReferenceId { get; set; }

    [JsonProperty("CreatedDate")]
    public DateTime CreatedDate { get; set; }

    [JsonProperty("ModifiedDate")]
    public DateTime ModifiedDate { get; set; }

    [JsonProperty("ModifiedDateString")]
    public string ModifiedDateString { get; set; }

    [JsonProperty("IsActive")]
    public bool IsActive { get; set; }
}

//create api response

public class CreateCustomAttributeApiResponseDto
{
    [JsonProperty("ApiStatus")]
    public bool ApiStatus { get; set; }

    [JsonProperty("Data")]
    public int Data { get; set; }

    [JsonProperty("Status")]
    public bool Status { get; set; }

    [JsonProperty("StatusMessage")]
    public string StatusMessage { get; set; }
}

public class CreateCustomAttributeDto
{
    [JsonProperty("Name")]
    public string Name { get; set; }

    [JsonProperty("Value")]
    public string Value { get; set; }

    [JsonProperty("Description")]
    public string Description { get; set; }

    [JsonProperty("CanEncrypt")]
    public bool CanEncrypt { get; set; }

    [JsonProperty("UserId")]
    public int UserId { get; set; }
}

public class UpdateCustomAttributeDto : CreateCustomAttributeDto
{
    [JsonProperty("Id")]
    public int Id { get; set; }
}

public class NonMasterDashboardResponse
{
    public List<NonMasterDashboardItem> Data { get; set; }
    public int TotalResults { get; set; }
    public List<LinkInfo> Links { get; set; }
}

public class NonMasterDashboardItem
{
    public bool CanRead { get; set; }
    public bool CanWrite { get; set; }
    public bool CanDelete { get; set; }
    public bool CanDownload { get; set; }
    public bool CanSchedule { get; set; }
    public bool CanOpen { get; set; }
    public bool CanMove { get; set; }
    public bool CanCopy { get; set; }
    public bool CanClone { get; set; }
    public bool CanCreateItem { get; set; }
    public string Id { get; set; }
    public string ItemType { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public int CreatedById { get; set; }
    public string CreatedByDisplayName { get; set; }
    public int ModifiedById { get; set; }
    public string ModifiedByFullName { get; set; }
    public string CategoryId { get; set; }
    public string CategoryName { get; set; }
    public string CreatedDate { get; set; }
    public string ModifiedDate { get; set; }
    public DateTime ItemModifiedDate { get; set; }
    public DateTime ItemCreatedDate { get; set; }
    public bool IsMultiDashboard { get; set; }
    public bool IsFavorite { get; set; }
    public bool IsPublic { get; set; }
    public bool IsLocked { get; set; }
    public bool IsUnlisted { get; set; }
    public List<TabDetail> TabDetail { get; set; }
    public string EmbedUrl { get; set; }
}
