using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Janvika.Finxplorer.Boldbi.Application.Contracts.Setup;

public class CreateUserDto
{
    public string Username { get; set; }
    public string Email { get; set; }
    public string FirstName { get; set; }
    public string LastName { get; set; }
    public string Password { get; set; }
}

public class CreateUserResponseDto
{
    public bool ApiStatus { get; set; }
    public int Data { get; set; }
    public bool Status { get; set; }
    public string StatusMessage { get; set; }
}

public class UserDto
{
    public int UserId { get; set; }
}

public class ExistingUserDto
{
    public int UserId { get; set; }
    public string Username { get; set; }
    public string Email { get; set; }
    public string FirstName { get; set; }
    public string LastName { get; set; }
    // Add other properties as needed
}
