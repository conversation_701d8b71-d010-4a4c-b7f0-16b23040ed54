﻿using Microsoft.EntityFrameworkCore;
using Volo.Abp;

namespace Janvika.Finxplorer.Boldbi.EntityFrameworkCore;

public static class BoldbiDbContextModelCreatingExtensions
{
    public static void ConfigureBoldbi(
        this ModelBuilder builder)
    {
        Check.NotNull(builder, nameof(builder));

        /* Configure all entities here. Example:

        builder.Entity<Question>(b =>
        {
            //Configure table & schema name
            b.ToTable(BoldbiDbProperties.DbTablePrefix + "Questions", BoldbiDbProperties.DbSchema);

            b.ConfigureByConvention();

            //Properties
            b.Property(q => q.Title).IsRequired().HasMaxLength(QuestionConsts.MaxTitleLength);

            //Relations
            b.Has<PERSON>any(question => question.Tags).WithOne().HasForeignKey(qt => qt.QuestionId);

            //Indexes
            b.HasIndex(q => q.CreationTime);
        });
        */
    }
}
