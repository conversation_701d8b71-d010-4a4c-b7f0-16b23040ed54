{"name": "Janvika.Finxplorer.Boldbi.EntityFrameworkCore", "hash": "", "contents": [{"namespace": "Janvika.Finxplorer.Boldbi.EntityFrameworkCore", "dependsOnModules": [{"declaringAssemblyName": "Janvika.Finxplorer.Boldbi.Domain", "namespace": "Janvika.Finxplorer.Boldbi", "name": "BoldbiDomainModule"}, {"declaringAssemblyName": "Volo.Abp.EntityFrameworkCore", "namespace": "Volo.Abp.EntityFrameworkCore", "name": "AbpEntityFrameworkCoreModule"}], "implementingInterfaces": [{"name": "IAbpModule", "namespace": "Volo.Abp.Modularity", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.Modularity.IAbpModule"}, {"name": "IOnPreApplicationInitialization", "namespace": "Volo.Abp.Modularity", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.Modularity.IOnPreApplicationInitialization"}, {"name": "IOnApplicationInitialization", "namespace": "Volo.Abp", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.IOnApplicationInitialization"}, {"name": "IOnPostApplicationInitialization", "namespace": "Volo.Abp.Modularity", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.Modularity.IOnPostApplicationInitialization"}, {"name": "IOnApplicationShutdown", "namespace": "Volo.Abp", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.IOnApplicationShutdown"}, {"name": "IPreConfigureServices", "namespace": "Volo.Abp.Modularity", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.Modularity.IPreConfigureServices"}, {"name": "IPostConfigureServices", "namespace": "Volo.Abp.Modularity", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.Modularity.IPostConfigureServices"}], "contentType": "abpModule", "name": "BoldbiEntityFrameworkCoreModule", "summary": null}, {"namespace": "Janvika.Finxplorer.Boldbi.EntityFrameworkCore", "connectionStringName": "<PERSON><PERSON>", "databaseTables": [], "replacedDbContexts": [], "modelBuilderExtensionMethods": [{"returnType": "Void", "namespace": "Janvika.Finxplorer.Boldbi.EntityFrameworkCore", "name": "ConfigureBoldbi", "summary": null, "isAsync": false, "isPublic": true, "isPrivate": false, "isStatic": true, "parameters": [{"type": "ModelBuilder", "name": "builder", "isOptional": false}]}], "implementingInterfaces": [{"name": "IInfrastructure<IServiceProvider>", "namespace": "Microsoft.EntityFrameworkCore.Infrastructure", "declaringAssemblyName": "Microsoft.EntityFrameworkCore", "fullName": "Microsoft.EntityFrameworkCore.Infrastructure.IInfrastructure<IServiceProvider>"}, {"name": "IDbContextDependencies", "namespace": "Microsoft.EntityFrameworkCore.Internal", "declaringAssemblyName": "Microsoft.EntityFrameworkCore", "fullName": "Microsoft.EntityFrameworkCore.Internal.IDbContextDependencies"}, {"name": "IDbSetCache", "namespace": "Microsoft.EntityFrameworkCore.Internal", "declaringAssemblyName": "Microsoft.EntityFrameworkCore", "fullName": "Microsoft.EntityFrameworkCore.Internal.IDbSetCache"}, {"name": "IDbContextPoolable", "namespace": "Microsoft.EntityFrameworkCore.Internal", "declaringAssemblyName": "Microsoft.EntityFrameworkCore", "fullName": "Microsoft.EntityFrameworkCore.Internal.IDbContextPoolable"}, {"name": "IResettableService", "namespace": "Microsoft.EntityFrameworkCore.Infrastructure", "declaringAssemblyName": "Microsoft.EntityFrameworkCore", "fullName": "Microsoft.EntityFrameworkCore.Infrastructure.IResettableService"}, {"name": "IDisposable", "namespace": "System", "declaringAssemblyName": "System.Private.CoreLib", "fullName": "System.IDisposable"}, {"name": "IAsyncDisposable", "namespace": "System", "declaringAssemblyName": "System.Private.CoreLib", "fullName": "System.IAsyncDisposable"}, {"name": "IAbpEfCoreDbContext", "namespace": "Volo.Abp.EntityFrameworkCore", "declaringAssemblyName": "Volo.Abp.EntityFrameworkCore", "fullName": "Volo.Abp.EntityFrameworkCore.IAbpEfCoreDbContext"}, {"name": "IEfCoreDbContext", "namespace": "Volo.Abp.EntityFrameworkCore", "declaringAssemblyName": "Volo.Abp.EntityFrameworkCore", "fullName": "Volo.Abp.EntityFrameworkCore.IEfCoreDbContext"}, {"name": "IAbpEfCoreDbFunctionContext", "namespace": "Volo.Abp.EntityFrameworkCore.GlobalFilters", "declaringAssemblyName": "Volo.Abp.EntityFrameworkCore", "fullName": "Volo.Abp.EntityFrameworkCore.GlobalFilters.IAbpEfCoreDbFunctionContext"}, {"name": "ITransientDependency", "namespace": "Volo.Abp.DependencyInjection", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.DependencyInjection.ITransientDependency"}, {"name": "IBoldbiDbContext", "namespace": "Janvika.Finxplorer.Boldbi.EntityFrameworkCore", "declaringAssemblyName": "Janvika.Finxplorer.Boldbi.EntityFrameworkCore", "fullName": "Janvika.Finxplorer.Boldbi.EntityFrameworkCore.IBoldbiDbContext"}], "contentType": "efCoreDbContext", "name": "BoldbiDbContext", "summary": null}]}