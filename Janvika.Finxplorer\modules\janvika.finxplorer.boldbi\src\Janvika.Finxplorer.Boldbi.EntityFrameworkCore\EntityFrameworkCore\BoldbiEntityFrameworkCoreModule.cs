﻿using Microsoft.Extensions.DependencyInjection;
using Volo.Abp.EntityFrameworkCore;
using Volo.Abp.Modularity;

namespace Janvika.Finxplorer.Boldbi.EntityFrameworkCore;

[DependsOn(
    typeof(BoldbiDomainModule),
    typeof(AbpEntityFrameworkCoreModule)
)]
public class BoldbiEntityFrameworkCoreModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        context.Services.AddAbpDbContext<BoldbiDbContext>(options =>
        {
                /* Add custom repositories here. Example:
                 * options.AddRepository<Question, EfCoreQuestionRepository>();
                 */
        });
    }
}
