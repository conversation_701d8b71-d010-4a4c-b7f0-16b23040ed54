using System;
using System.Linq;
using System.Threading.Tasks;
using Janvika.Finxplorer.Boldbi.Application.Contracts.Setup;
using Janvika.Finxplorer.Boldbi.Application.Services.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Volo.Abp;

namespace Janvika.Finxplorer.Boldbi.Application.Setup;

public class TenantListAppService : BoldbiAppService, IBoldBiTenantListAppService
{
    private readonly EmbedProperties _embedProperties;

    private readonly IHttpService<
        TenantListResponseDto,
        object,
        object,
        object,
        object,
        object
    > _tenantHttpService;

    private readonly ILogger<TenantListAppService> _logger;

    public TenantListAppService(
        IOptions<EmbedProperties> embedPropertiesOptions,
        ILogger<TenantListAppService> logger,
        IHttpService<
            TenantListResponseDto,
            object,
            object,
            object,
            object,
            object
        > tenantHttpService
    )
    {
        _embedProperties = embedPropertiesOptions.Value;
        _logger = logger;
        _tenantHttpService = tenantHttpService;
    }

    private async Task GetTenantTokenAsync()
    {
        try
        {
            var tokenRequest = TokenRequest.CreateClientCredentialsGrant(
                _embedProperties.BoldBITenantClientId,
                _embedProperties.BoldBITenantClientSecret
            );

            string tokenUrl = $"/api/token";

            await _tenantHttpService.AuthenticateAsync(tokenRequest, tokenUrl);
        }
        catch (Exception ex)
        {
            throw new UserFriendlyException("Failed to get tenant token.", ex.Message);
        }
    }

    public async Task<string> FindTenantByNameWithPaginationAsync(string tenantName)
    {
        await GetTenantTokenAsync();

        const int pageSize = 10; // Max allowed page size
        int currentPage = 1;
        int totalPages = 1;

        do
        {
            try
            {
                var url = $"/api/v2.0/tenants?page={currentPage}&page_size={pageSize}";
                var response = await _tenantHttpService.GetAsync(url);

                if (response?.Data == null)
                {
                    _logger.LogWarning("No tenants found on page {Page}", currentPage);
                    return null;
                }

                var foundTenant = response.Data.FirstOrDefault(t =>
                    t.TenantName.Equals(tenantName, StringComparison.OrdinalIgnoreCase)
                );

                if (foundTenant != null)
                {
                    _logger.LogInformation(
                        "Found tenant {TenantName} on page {Page}",
                        tenantName,
                        currentPage
                    );
                    return foundTenant.Id;
                }

                totalPages = (int)Math.Ceiling(response.TotalResults / (double)pageSize);

                if (currentPage >= totalPages)
                {
                    _logger.LogWarning(
                        "Tenant {TenantName} not found after searching all {Pages} pages",
                        tenantName,
                        totalPages
                    );
                    return null;
                }

                currentPage++;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error searching for tenant {TenantName} on page {Page}",
                    tenantName,
                    currentPage
                );
                throw;
            }
        } while (currentPage <= totalPages);

        return null;
    }
}
