@page "/DataExplorer"

@attribute [Authorize(BoldbiPermissions.Tenant.Default)]
@using Janvika.Finxplorer.Boldbi.Application.Contracts.Permissions
@using Janvika.Finxplorer.Boldbi.Application.Contracts.Setup
@using Janvika.Finxplorer.Quiltt.AccountDetail 
@using Microsoft.AspNetCore.Authorization
@using Volo.Abp.AspNetCore.Components.Web.Theming.Layout
@using Blazorise;
@inject ITransactionsAppService TransactionsAppService 

@inherits BoldbiComponentBase

<style>
    .data-explorer-page {
        height: 100vh;
        display: flex;
        flex-direction: column;
    }
    
    .sand-dance-container {
        flex: 1;
        display: flex;
        flex-direction: column;
        min-height: 0;
        position: relative;
    }
    
    .sand-dance-container iframe {
        flex: 1;
        width: 100%;
        border: 1px solid #ccc;
        min-height: 500px;
    }
</style>

<div class="data-explorer-page">
    @* ************************* PAGE HEADER ************************* *@
    <PageHeader BreadcrumbItems="@BreadcrumbItems">
    </PageHeader>

    <div class="sand-dance-container">
        <iframe id="sandDanceIframe" src="https://microsoft.github.io/SandDance/embed/v4/sanddance-embed.html">
        </iframe>
    </div>
</div>
