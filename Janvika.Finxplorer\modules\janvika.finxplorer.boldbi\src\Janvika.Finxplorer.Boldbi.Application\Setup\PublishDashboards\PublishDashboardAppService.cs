using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using Janvika.Finxplorer.Boldbi.Application;
using Janvika.Finxplorer.Boldbi.Application.Contracts.Setup;
using Janvika.Finxplorer.Boldbi.Application.Services.Http;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Volo.Abp;

namespace Janvika.Finxplorer.BoldBI.Application.Setup;

public class PublishDashboardAppService : BoldbiAppService, IPublishDashboardAppService
{
    const string successStatus = "Success";
    private readonly EmbedProperties _embedProperties;
    private readonly IDashboardAppService _dashboardAppService;
    private readonly IDataSourcesAppService _dataSourcesAppService;
    private readonly IPublishSyncAppService _publishSyncAppService;

    private readonly IHttpService<
        PublishItemsResponseDto,
        PublishDashboardDto,
        object,
        object,
        object,
        PublishDashboardApiResponseDto
    > _publishService;

    public PublishDashboardAppService(
        IOptions<EmbedProperties> embedPropertiesOptions,
        IDashboardAppService dashboardAppService,
        IDataSourcesAppService dataSourcesAppService,
        IPublishSyncAppService publishSyncAppService,
        IHttpService<
            PublishItemsResponseDto,
            PublishDashboardDto,
            object,
            object,
            object,
            PublishDashboardApiResponseDto
        > publishService
    )
    {
        _embedProperties = embedPropertiesOptions.Value;
        _dashboardAppService = dashboardAppService;
        _dataSourcesAppService = dataSourcesAppService;
        _publishService = publishService;
        _publishSyncAppService = publishSyncAppService;
    }

    public async Task GetAdminTokenAsync()
    {
        var tokenRequest = TokenRequest.CreatePasswordGrant(
            _embedProperties.UserEmail,
            _embedProperties.UserPassword
        );

        string masterSiteTokenUrl = $"/bi/api/{_embedProperties.SiteIdentifier}/token";

        await _publishService.AuthenticateAsync(tokenRequest, masterSiteTokenUrl);
    }

    public async Task PublishDashboardAsync(TenantResponseDto boldBiTenant)
    {
        try
        {
            // Authenticate
            await GetAdminTokenAsync();

            var readyForPublishDashboardList = await PrepInputForPublish(boldBiTenant);

            foreach (var dashboardToBePublished in readyForPublishDashboardList)
            {
                // First, check if the dashboard is already published
                var (exists, publishId) = await CheckDashboardPublishStatusAsync(
                    dashboardToBePublished.ItemId,
                    boldBiTenant.Data.Id
                );

                if (exists)
                    await _publishSyncAppService.SynchronizeDashboardAsync(
                        dashboardToBePublished.ItemId,
                        publishId
                    );
                else
                    await NewPublishDashboardAsync(dashboardToBePublished);
            }
        }
        catch (HttpRequestException ex)
        {
            throw new UserFriendlyException(
                "Failed to publish or check dashboard status." + ex.Message
            );
        }
    }

    private async Task<List<PublishDashboardDto>> PrepInputForPublish(
        TenantResponseDto boldBiTenant
    )
    {
        List<PublishDashboardDto> publishDtos = new List<PublishDashboardDto>();

        var dashboards = await _dashboardAppService.GetDashboardsAsync();

        var filteredDashboards = dashboards.Where(d =>
            !string.Equals(d.CategoryName, "Others", StringComparison.OrdinalIgnoreCase)
        );

        foreach (var dashboard in filteredDashboards)
        {
            var allDataSources = await _dataSourcesAppService.GetDataSourcesAsync();

            publishDtos.Add(
                new PublishDashboardDto
                {
                    ItemId = dashboard.Id,
                    TargetSiteDetails = new List<TargetSiteDetailDto>
                    {
                        new TargetSiteDetailDto
                        {
                            ClientId = boldBiTenant.Data.Id,
                            SiteIdentifier = boldBiTenant.Data.TenantName,
                            CategoryName = dashboard.CategoryName,
                            Description =
                                $"Published dashboard {dashboard.Name} for the user under the category {dashboard.CategoryName}",
                            UseSourceItemName = true,
                            LockDashboard = true,
                            LockDatasource = true,
                            Datasources = allDataSources
                                .Select(ds => new DatasourceDto { Id = ds.Id, IsLocked = true })
                                .ToList(),
                            PublishType = "Internal",
                        },
                    },
                }
            );
        }

        return publishDtos;
    }

    private async Task NewPublishDashboardAsync(PublishDashboardDto publishDto)
    {
        await GetAdminTokenAsync();

        var publishResponse = await _publishService.CreateAsyncWithStatusResponse(
            $"/bi/api/{_embedProperties.SiteIdentifier}/v5.0/publish/item",
            publishDto
        );

        if (!publishResponse.ApiStatus || !publishResponse.Status)
        {
            throw new UserFriendlyException(publishResponse.StatusMessage);
        }

        // Check the status of the newly published dashboard
        //  await Task.Delay(10000); // Wait for 10 seconds to allow for processing

        // await _publishService.GetAsync(
        //     $"/bi/api/{_embedProperties.SiteIdentifier}/v5.0/publish/items?itemType=Dashboard"
        // );
    }

    private async Task<(bool exists, string? publishId)> CheckDashboardPublishStatusAsync(
        string itemId,
        string tenantId
    )
    {
        await GetAdminTokenAsync();

        List<PublishItemDto> alreadyPublishedItems = await GetAllPublishedDashboards();

        var existingPublish = alreadyPublishedItems?.FirstOrDefault(item =>
            item.ItemId == itemId && item.TenantId == tenantId
        );

        return (existingPublish != null, existingPublish?.PublishId);
    }

    private async Task<List<PublishItemDto>> GetAllPublishedDashboards()
    {
        try
        {
            await GetAdminTokenAsync();

            const int pageSize = 25;
            int currentPage = 1;
            int totalPages = 1;
            var allPublishedItems = new List<PublishItemDto>();

            do
            {
                try
                {
                    var response = await _publishService.GetAsync(
                        $"/bi/api/{_embedProperties.SiteIdentifier}/v5.0/publish/items?itemType=Dashboard&page={currentPage}&page_size={pageSize}"
                    );

                    if (response?.Data != null)
                    {
                        allPublishedItems.AddRange(response.Data);
                    }

                    totalPages = (int)Math.Ceiling(response.TotalResults / (double)pageSize);

                    if (currentPage >= totalPages)
                    {
                        break;
                    }

                    currentPage++;
                }
                catch (HttpServiceException ex)
                {
                    throw new UserFriendlyException($"Failed to get published items: {ex.Message}");
                }
            } while (currentPage <= totalPages);

            return allPublishedItems;
        }
        catch (Exception ex)
        {
            throw new UserFriendlyException($"Failed to check published items: {ex.Message}");
        }
    }
}
