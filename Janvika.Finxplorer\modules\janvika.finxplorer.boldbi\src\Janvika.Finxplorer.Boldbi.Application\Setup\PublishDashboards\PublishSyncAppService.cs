using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using Janvika.Finxplorer.Boldbi.Application;
using Janvika.Finxplorer.Boldbi.Application.Contracts.Setup;
using Janvika.Finxplorer.Boldbi.Application.Services.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Volo.Abp;

namespace Janvika.Finxplorer.BoldBI.Application.Setup;

public class PublishSyncAppService : BoldbiAppService, IPublishSyncAppService
{
    private readonly EmbedProperties _embedProperties;
    private readonly IHttpService<
        object,
        object,
        object,
        object,
        object,
        SynchronizeDashboardApiResponseDto
    > _publishService;
    private readonly ILogger<DataSourcesAppService> _logger;

    public PublishSyncAppService(
        IOptions<EmbedProperties> embedPropertiesOptions,
        ILogger<DataSourcesAppService> logger,
        IHttpService<
            object,
            object,
            object,
            object,
            object,
            SynchronizeDashboardApiResponseDto
        > publishService
    )
    {
        _embedProperties = embedPropertiesOptions.Value;
        _logger = logger;
        _publishService = publishService;
    }

    public async Task GetAdminTokenAsync()
    {
        var tokenRequest = TokenRequest.CreatePasswordGrant(
            _embedProperties.UserEmail,
            _embedProperties.UserPassword
        );

        string masterSiteTokenUrl = $"/bi/api/{_embedProperties.SiteIdentifier}/token";

        await _publishService.AuthenticateAsync(tokenRequest, masterSiteTokenUrl);
    }

    public async Task SynchronizeDashboardAsync(string itemId, string publishId)
    {
        await GetAdminTokenAsync();

        var syncRequest = new SynchronizePublishItemDto
        {
            IsBulkSynchronize = true,
            PublishId = new List<string> { publishId },
        };

        var result = await _publishService.CreateAsyncWithStatusResponse(
            $"/bi/api/{_embedProperties.SiteIdentifier}/v5.0/publish/items/{itemId}/synchronize",
            syncRequest
        );

        if (!result.Status)
        {
            throw new Exception($"Dashboard synchronization failed: {result.StatusMessage}");
        }
    }
}
