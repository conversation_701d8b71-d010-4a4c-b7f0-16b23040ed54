using Janvika.Finxplorer.Quiltt;
using System.Net.Http;
using Janvika.Finxplorer.Boldbi.Application.Contracts;
using Janvika.Finxplorer.Boldbi.Application.Contracts.Setup;
using Janvika.Finxplorer.Boldbi.Application.Services.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Volo.Abp.Application;
using Volo.Abp.AutoMapper;
using Volo.Abp.Modularity;

namespace Janvika.Finxplorer.Boldbi.Application;

[DependsOn(
    typeof(QuilttApplicationContractsModule),
    typeof(BoldbiDomainModule),
    typeof(BoldbiApplicationContractsModule),
    typeof(AbpDddApplicationModule),
    typeof(AbpAutoMapperModule)
)]
public class BoldbiApplicationModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        var configuration = context.Services.GetConfiguration();
        context.Services.Configure<EmbedProperties>(configuration.GetSection("BoldBI"));
        context.Services.AddAutoMapperObjectMapper<BoldbiApplicationModule>();
        Configure<AbpAutoMapperOptions>(options =>
        {
            options.AddMaps<BoldbiApplicationModule>(validate: true);
        });

        AddBoldbiHttpServices(context.Services, configuration);
    }

    public static IServiceCollection AddBoldbiHttpServices(
        IServiceCollection? services,
        IConfiguration configuration
    )
    {
        // Get base URL from config
        var boldbiOptions = configuration.GetSection("Boldbi").Get<EmbedProperties>();
        var baseUrl = boldbiOptions.ServerUrl;

        // Register HttpClient factory
        services.AddHttpClient();

        services.AddScoped<IHttpService<UserDto, CreateUserDto, object, object, object, object>>(
            sp => new HttpService<UserDto, CreateUserDto, object, object, object, object>(
                sp.GetRequiredService<IHttpClientFactory>(),
                baseUrl
            )
        );

        services.AddScoped<
            IHttpService<
                TenantResponseDto,
                CreateTenantInputDto,
                object,
                TenantListResponseDto,
                object,
                CreateTenantApiResponseDto
            >
        >(sp => new HttpService<
            TenantResponseDto,
            CreateTenantInputDto,
            object,
            TenantListResponseDto,
            object,
            CreateTenantApiResponseDto
        >(sp.GetRequiredService<IHttpClientFactory>(), baseUrl));

        // Dashboard services
        services.AddScoped<IHttpService<DashboardResponse, object, object, object, object, object>>(
            sp => new HttpService<DashboardResponse, object, object, object, object, object>(
                sp.GetRequiredService<IHttpClientFactory>(),
                baseUrl
            )
        );

        // DataSource service
        services.AddScoped<
            IHttpService<DataSourceResponseDto, object, object, object, object, object>
        >(sp => new HttpService<DataSourceResponseDto, object, object, object, object, object>(
            sp.GetRequiredService<IHttpClientFactory>(),
            baseUrl
        ));

        // Dashboard services 2
        services.AddScoped<
            IHttpService<NonMasterDashboardResponse, object, object, object, object, object>
        >(sp => new HttpService<NonMasterDashboardResponse, object, object, object, object, object>(
            sp.GetRequiredService<IHttpClientFactory>(),
            baseUrl
        ));

        // Custom Attribute service
        services.AddScoped<
            IHttpService<
                CustomAttributeResponseDto,
                object,
                UpdateCustomAttributeDto,
                object,
                object,
                CreateCustomAttributeApiResponseDto
            >
        >(sp => new HttpService<
            CustomAttributeResponseDto,
            object,
            UpdateCustomAttributeDto,
            object,
            object,
            CreateCustomAttributeApiResponseDto
        >(sp.GetRequiredService<IHttpClientFactory>(), baseUrl));

        // Publish services
        services.AddScoped<
            IHttpService<
                PublishItemsResponseDto,
                PublishDashboardDto,
                object,
                object,
                object,
                PublishDashboardApiResponseDto
            >
        >(sp => new HttpService<
            PublishItemsResponseDto,
            PublishDashboardDto,
            object,
            object,
            object,
            PublishDashboardApiResponseDto
        >(sp.GetRequiredService<IHttpClientFactory>(), baseUrl));

        services.AddScoped<
            IHttpService<object, object, object, object, object, SynchronizeDashboardApiResponseDto>
        >(sp => new HttpService<
            object,
            object,
            object,
            object,
            object,
            SynchronizeDashboardApiResponseDto
        >(sp.GetRequiredService<IHttpClientFactory>(), baseUrl));

        // Additional Tenant service
        services.AddScoped<
            IHttpService<TenantListResponseDto, object, object, object, object, object>
        >(sp => new HttpService<TenantListResponseDto, object, object, object, object, object>(
            sp.GetRequiredService<IHttpClientFactory>(),
            baseUrl
        ));

        return services;
    }
}
