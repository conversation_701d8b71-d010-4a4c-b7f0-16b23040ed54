using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using Janvika.Finxplorer.Boldbi.Application.Contracts.Permissions;
using Janvika.Finxplorer.Boldbi.Application.Contracts.Setup;
using Janvika.Finxplorer.Boldbi.Application.Services.Http;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Volo.Abp;

namespace Janvika.Finxplorer.Boldbi.Application.Setup;

[Authorize(BoldbiPermissions.Tenant.Default)]
public class NonMasterDashboardAppService : BoldbiAppService, INonMasterDashboardAppService
{
    private readonly EmbedProperties _embedProperties;
    private readonly IHttpService<
        NonMasterDashboardResponse,
        object,
        object,
        object,
        object,
        object
    > _nonMasterDashboardHttpService;

    public NonMasterDashboardAppService(
        IOptions<EmbedProperties> embedPropertiesOptions,
        IHttpService<
            NonMasterDashboardResponse,
            object,
            object,
            object,
            object,
            object
        > nonMasterDashboardHttpService
    )
    {
        _embedProperties = embedPropertiesOptions.Value;
        _nonMasterDashboardHttpService = nonMasterDashboardHttpService;
    }

    public async Task<List<NonMasterDashboardItem>> ListDashboards(
        string Username,
        string universalTenantName //TenantResponseDto tenant
    )
    {
        try
        {
            // Authenticate first
            await Authenticate(Username, _embedProperties.StandardPassword, universalTenantName);

            var allDashboards = new List<NonMasterDashboardItem>();
            var pageSize = 25;
            var currentPage = 1;

            while (true)
            {
                try
                {
                    var endpoint =
                        $"/bi/api/site/{universalTenantName}/v5.0/dashboards?page={currentPage}&page_size={pageSize}";

                    var dashboardResponse = await _nonMasterDashboardHttpService.GetAsync(endpoint);

                    if (dashboardResponse?.Data == null)
                    {
                        break;
                    }

                    allDashboards.AddRange(dashboardResponse.Data);

                    var totalPages = (int)
                        Math.Ceiling(dashboardResponse.TotalResults / (double)pageSize);
                    if (currentPage >= totalPages)
                    {
                        break;
                    }

                    currentPage++;
                }
                catch (HttpServiceException ex)
                {
                    throw new UserFriendlyException(
                        $"Failed to fetch dashboard page {currentPage}: {ex.Message}"
                    );
                }
            }

            if (!allDashboards.Any())
            {
                throw new UserFriendlyException("No dashboards found.");
            }

            return allDashboards;
        }
        catch (Exception ex)
        {
            throw new UserFriendlyException($"Failed to list dashboards: {ex.Message}");
        }
    }

    private async Task Authenticate(string username, string password, string universalTenantName)
    {
        try
        {
            var tokenRequest = TokenRequest.CreatePasswordGrant(username, password);
            string tokenUrl = $"/bi/api/site/{universalTenantName}/token";

            await _nonMasterDashboardHttpService.AuthenticateAsync(tokenRequest, tokenUrl);
        }
        catch (Exception ex)
        {
            throw new UserFriendlyException($"Authentication failed: {ex.Message}");
        }
    }
}
