using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Janvika.Finxplorer.Boldbi.Application.Contracts.Setup;

public class PublishDashboardDto
{
    [JsonProperty("ItemId")]
    public string ItemId { get; set; }

    [JsonProperty("TargetSiteDetails")]
    public List<TargetSiteDetailDto> TargetSiteDetails { get; set; }
}

public class TargetSiteDetailDto
{
    [JsonProperty("ClientId")]
    public string ClientId { get; set; }

    [JsonProperty("SiteIdentifier")]
    public string SiteIdentifier { get; set; }

    [JsonProperty("CategoryName")]
    public string CategoryName { get; set; }

    [JsonProperty("Description")]
    public string Description { get; set; }

    [JsonProperty("UseSourceItemName")]
    public bool UseSourceItemName { get; set; }

    [JsonProperty("LockDashboard")]
    public bool LockDashboard { get; set; }

    [<PERSON>son<PERSON>roperty("LockDatasource")]
    public bool LockDatasource { get; set; }

    [JsonProperty("Datasources")]
    public List<DatasourceDto> Datasources { get; set; }

    [JsonProperty("PublishType")]
    public string PublishType { get; set; }
}

public class DatasourceDto
{
    [JsonProperty("Id")]
    public string Id { get; set; }

    [JsonProperty("IsLocked")]
    public bool IsLocked { get; set; }
}
