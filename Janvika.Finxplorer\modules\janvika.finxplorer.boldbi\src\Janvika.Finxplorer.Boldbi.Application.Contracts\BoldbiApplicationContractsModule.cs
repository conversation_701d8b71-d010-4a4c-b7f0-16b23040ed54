using Janvika.Finxplorer.Quiltt;
using Volo.Abp.Application;
using Volo.Abp.Authorization;
using Volo.Abp.Modularity;

namespace Janvika.Finxplorer.Boldbi.Application.Contracts;

[DependsOn(
    typeof(QuilttApplicationContractsModule),
    typeof(BoldbiDomainSharedModule),
    typeof(AbpDddApplicationContractsModule),
    typeof(AbpAuthorizationModule)
)]
public class BoldbiApplicationContractsModule : AbpModule { }
