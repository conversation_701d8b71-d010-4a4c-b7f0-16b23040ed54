using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Janvika.Finxplorer.Boldbi.Application.Contracts.Setup;

public class EmbedClass
{
    public string EmbedQuerString { get; set; }
    public string DashboardServerApiUrl { get; set; }
}

public class EmbedProperties
{
    public string ServerUrl { get; set; }
    public string SiteIdentifier { get; set; }
    public string Environment { get; set; }
    public string UserEmail { get; set; }
    public string UserPassword { get; set; }
    public string EmbedSecret { get; set; }
    public string EmbedType { get; set; }
    public string DashboardId { get; set; }
    public string BoldBITenantClientId { get; set; }
    public string BoldBITenantClientSecret { get; set; }
    public string DefaultCategoryName { get; set; }
    public string DataSourceId { get; set; }

    public string DatabaseServerName { get; set; }
    public string DatabaseUserName { get; set; }
    public string DatabasePassword { get; set; }
    public string AzureBlobStorageUri { get; set; }
    public string AzureBlobAccountName { get; set; }
    public string AzureBlobAccessKey { get; set; }
    public string StandardPassword { get; set; }
}

public class TokenObject
{
    [JsonProperty("access_token")]
    public string AccessToken { get; set; }

    [JsonProperty("token_type")]
    public string TokenType { get; set; }

    [JsonProperty("expires_in")]
    public int ExpiresIn { get; set; }

    [JsonProperty("email")]
    public string Email { get; set; }

    public string LoginResult { get; set; }

    public string LoginStatusInfo { get; set; }

    [JsonProperty(".issued")]
    public string Issued { get; set; }

    [JsonProperty(".expires")]
    public DateTime ExpiresAt { get; set; }
}
