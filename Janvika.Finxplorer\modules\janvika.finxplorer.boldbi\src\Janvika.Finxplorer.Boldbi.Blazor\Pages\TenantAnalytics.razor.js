let dotNetReference;
let dashboardInstance = null;
let isFullscreenMode = false;

export function setDotNetReference(reference) {
  dotNetReference = reference;
  // Setup escape key listener for fullscreen
  setupFullscreenListeners();
}

// Setup fullscreen event listeners
function setupFullscreenListeners() {
  // Listen for Escape key to exit fullscreen
  document.addEventListener("keydown", function (event) {
    if (event.key === "Escape" && isFullscreenMode) {
      exitFullscreen();
    }
  });

  // Listen for browser fullscreen changes
  document.addEventListener("fullscreenchange", handleFullscreenChange);
  document.addEventListener("webkitfullscreenchange", handleFullscreenChange);
  document.addEventListener("mozfullscreenchange", handleFullscreenChange);
  document.addEventListener("MSFullscreenChange", handleFullscreenChange);
}

// Enter fullscreen mode
export function enterFullscreen() {
  try {
    isFullscreenMode = true;

    // Add CSS class to body for styling
    document.body.classList.add("fullscreen-active");

    // Get dashboard container
    const dashboardContainer = document.getElementById("dashboard");
    if (dashboardContainer) {
      dashboardContainer.classList.add("fullscreen-mode");
    }

    // Try to use browser's native fullscreen API as well
    const element = dashboardContainer || document.documentElement;
    if (element.requestFullscreen) {
      element.requestFullscreen().catch((err) => {
        console.log("Native fullscreen not available:", err);
      });
    } else if (element.webkitRequestFullscreen) {
      element.webkitRequestFullscreen();
    } else if (element.mozRequestFullScreen) {
      element.mozRequestFullScreen();
    } else if (element.msRequestFullscreen) {
      element.msRequestFullscreen();
    }

    // Resize dashboard after entering fullscreen
    setTimeout(() => {
      if (dashboardInstance) {
        try {
          dashboardInstance.resizeDashboard();
        } catch (error) {
          console.log("Dashboard resize error:", error);
        }
      }
    }, 300);

    console.log("Entered fullscreen mode");
    return true;
  } catch (error) {
    console.error("Error entering fullscreen:", error);
    return false;
  }
}

// Exit fullscreen mode
export function exitFullscreen() {
  try {
    isFullscreenMode = false;

    // Remove CSS classes
    document.body.classList.remove("fullscreen-active");

    const dashboardContainer = document.getElementById("dashboard");
    if (dashboardContainer) {
      dashboardContainer.classList.remove("fullscreen-mode");
    }

    // Exit browser's native fullscreen
    if (document.exitFullscreen) {
      document.exitFullscreen().catch((err) => {
        console.log("Native fullscreen exit error:", err);
      });
    } else if (document.webkitExitFullscreen) {
      document.webkitExitFullscreen();
    } else if (document.mozCancelFullScreen) {
      document.mozCancelFullScreen();
    } else if (document.msExitFullscreen) {
      document.msExitFullscreen();
    }

    // Resize dashboard after exiting fullscreen
    setTimeout(() => {
      if (dashboardInstance) {
        try {
          dashboardInstance.resizeDashboard();
        } catch (error) {
          console.log("Dashboard resize error:", error);
        }
      }
    }, 300);

    console.log("Exited fullscreen mode");

    // Notify Blazor component about the state change
    if (dotNetReference) {
      dotNetReference.invokeMethodAsync("OnFullscreenExited");
    }

    return true;
  } catch (error) {
    console.error("Error exiting fullscreen:", error);
    return false;
  }
}

// Handle browser fullscreen change events
function handleFullscreenChange() {
  const isInFullscreen = !!(
    document.fullscreenElement ||
    document.webkitFullscreenElement ||
    document.mozFullScreenElement ||
    document.msFullscreenElement
  );

  if (!isInFullscreen && isFullscreenMode) {
    // User exited fullscreen via browser controls (F11, etc.)
    exitFullscreen();
  }
}

// Get current fullscreen state
export function getFullscreenState() {
  return isFullscreenMode;
}

export async function renderDashboard(boldBIConfig) {
  // // console.log("Rendering dashboard with config:", boldBIConfig);

  const container = document.getElementById("dashboard");
  if (!container) {
    console.error("Dashboard container not found");
    return;
  }

  try {
    var boldbi_obj = BoldBI.create({
      serverUrl: boldBIConfig.ServerUrl + "/bi/" + boldBIConfig.SiteIdentifier,
      dashboardId: boldBIConfig.DashboardId,
      embedContainerId: "dashboard",
      mode: BoldBI.Mode.View,
      embedType: "component",
      environment: "onpremise",
      width: "100%",
      height: "800px",
      expirationTime: 10000,
      enableComment: true,
      enableFullScreen: true,
      embedAiAssistant: {
        enableAiAssistant: true,
        aiAssistantPosition: "bottom",
        enableAiSummary: true,
        enableWidgetSummary: true,
        enableDashboardSummary: true,
        hideAiDataUsage: false,
      },
      widgetSettings: {
        enableComment: true,
        enableExport: true,
        enableMaximize: true,
        enableView: true,
        enableFilter: true,
      },
      dashboardSettings: {
        showHeader: true,
        showExport: true,
        showRefresh: true,
        showMoreOption: true,
      },
      beforeNavigateUrlLinking: function (args) {
        // // console.log("beforeNavigateUrlLinking triggered:", args);

        if (!args || !args.linkInfo || !args.linkInfo.parameterDetails) {
          // // console.log("No valid link info found");
          return;
        }

        const transactionId = args.linkInfo.parameterDetails.find(
          (p) => p.parameter === "Transaction Id"
        )?.value;

        // // console.log("Found transaction ID:", transactionId);

        if (transactionId && dotNetReference) {
          args.cancel = true; // Prevent default navigation
          try {
            dotNetReference.invokeMethodAsync(
              "HandleWidgetTransactionSelection",
              transactionId
            );
            // console.log("Successfully invoked HandleWidgetTransactionSelection");
          } catch (error) {
            //console.error("Error invoking HandleWidgetTransactionSelection:", error);
          }
        }
      },
      authorizationServer: {
        url: boldBIConfig.AuthorizationServerBaseUrl,
        headers: {
          RequestVerificationToken: boldBIConfig.RequestVerificationToken,
          accept: "text/plain",
          "Content-Type": "application/json",
          "X-Requested-With": "XMLHttpRequest",
        },
      },
    });

    // console.log("BoldBI object created successfully");
    dashboardInstance = boldbi_obj;

    await boldbi_obj.loadDashboard();
    // console.log("Dashboard loaded successfully");
  } catch (error) {
    // console.error("Error in dashboard initialization:", error);
    // console.error("Stack trace:", error.stack);
    throw error;
  }
}

// Helper function to check dashboard state
export function checkDashboardState() {
  try {
    const instance = BoldBI.getInstance("dashboard");
    if (!instance) {
      // console.error("No dashboard instance found");
      return;
    }

    // console.log("Current dashboard instance:", instance);

    instance.getDashboardWidgets((widgets) => {
      if (!widgets) {
        // console.error("No widgets found");
        return;
      }
      // console.log("Available widgets:", widgets);
    });
  } catch (error) {
    // console.error("Error checking dashboard state:", error);
  }
}

// Global error handler
window.onerror = function (msg, url, lineNo, columnNo, error) {
  // console.error("Global error: ", {
  //   message: msg,
  //   url: url,
  //   lineNo: lineNo,
  //   columnNo: columnNo,
  //   error: error,
  // });
  return false;
};
