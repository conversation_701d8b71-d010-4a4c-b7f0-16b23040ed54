using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using Janvika.Finxplorer.Boldbi.Application.Contracts.Setup;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Volo.Abp;

namespace Janvika.Finxplorer.Boldbi.Application.Setup;

public class BoldBiWorkflowAppService : BoldbiAppService, IBoldBiWorkflowAppService
{
    private readonly EmbedProperties _embedProperties;

    private readonly ILogger<BoldBiWorkflowAppService> _logger;
    private readonly IBoldBiUserAppService _boldBiUserAppService;
    private readonly ITenantAppService _boldBiTenantAppService;
    private readonly IPublishDashboardAppService _boldBiPublishDashboardAppService;
    private readonly INonMasterSiteAppService _boldBiNonMasterSiteAppService;

    public BoldBiWorkflowAppService(
        IOptions<EmbedProperties> embedPropertiesOptions,
        IHttpClientFactory httpClientFactory,
        ILogger<BoldBiWorkflowAppService> logger,
        IBoldBiUserAppService boldBiUserAppService,
        ITenantAppService boldBiTenantAppService,
        IPublishDashboardAppService boldBiPublishDashboardAppService,
        INonMasterSiteAppService boldBiNonMasterSiteAppService,
        INonMasterDashboardAppService boldBiNonMasterDashboardAppService
    )
    {
        _embedProperties = embedPropertiesOptions.Value;
        _logger = logger;
        _embedProperties = embedPropertiesOptions.Value;
        _boldBiUserAppService = boldBiUserAppService;
        _boldBiTenantAppService = boldBiTenantAppService;
        _boldBiPublishDashboardAppService = boldBiPublishDashboardAppService;
        _boldBiNonMasterSiteAppService = boldBiNonMasterSiteAppService;
    }

    public async Task ExecuteAsync(BoldBiSetupJobArgs args)
    {
        _logger.LogInformation(
            "BoldBiWorkflowAppService:Starting Bold BI setup for ABP tenant: {TenantId}",
            args.AbpTenantName
        );

        try
        {
            // Step 1: Create a new user
            var user = await GetorCreateUserAsync(args);

            // // Step 2: Create a new tenant
            var tenant = await GetOrCreateTenantAsync(args);

            // // Step 3: Publish dashboard
            await PublishDashboardAsync(tenant);

            // //TODO
            // // Check target site for dashboard publish status


            // // Step 4: Create custom attribute
            await CreateCustomAttributeInNewTenantAsync(args, tenant);

            _logger.LogInformation(
                "BoldBiWorkflowAppService:Bold BI setup completed successfully for ABP tenant: {TenantId}",
                args.AbpTenantName
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "BoldBiWorkflowAppService:Error during Bold BI setup for ABP tenant: {TenantId}",
                args.AbpTenantName
            );
            throw;
        }
    }

    private async Task<UserDto> GetorCreateUserAsync(BoldBiSetupJobArgs args)
    {
        _logger.LogInformation(
            "BoldBiWorkflowAppService:Creating Bold BI user for ABP tenant: {TenantId}",
            args.AbpTenantName
        );

        try
        {
            return await _boldBiUserAppService.GetorCreateUserAsync(args);
        }
        catch (Exception ex) when (ex.Message.Contains("Username already exists"))
        {
            _logger.LogWarning(
                "BoldBiWorkflowAppService:User already exists. Fetching existing user details."
            );
            // Here you would typically fetch the existing user's details
            // For this example, we're creating a dummy UserDto with a placeholder ID
            return new UserDto { UserId = -1 };
        }
    }

    private async Task<TenantResponseDto> GetOrCreateTenantAsync(BoldBiSetupJobArgs args)
    {
        _logger.LogInformation(
            "BoldBiWorkflowAppService:Creating Bold BI tenant for ABP tenant: {TenantId}",
            args.AbpTenantName
        );

        var boldBiTenant = await _boldBiTenantAppService.GetOrCreateTenantAsync(args);
        var tenantDetails = await _boldBiTenantAppService.GetTenantAsync(boldBiTenant.TenantId);

        _logger.LogInformation(
            "BoldBiWorkflowAppService:Created Bold BI tenant: {TenantId}",
            boldBiTenant.TenantId
        );

        return tenantDetails;
    }

    private async Task PublishDashboardAsync(TenantResponseDto boldBiTenant)
    {
        _logger.LogInformation(
            "BoldBiWorkflowAppService:Publishing dashboard for Bold BI tenant: {TenantId}",
            boldBiTenant.Data.Id
        );
        await _boldBiPublishDashboardAppService.PublishDashboardAsync(boldBiTenant);
    }

    private async Task CreateCustomAttributeInNewTenantAsync(
        BoldBiSetupJobArgs args,
        TenantResponseDto tenant
    )
    {
        _logger.LogInformation(
            "BoldBiWorkflowAppService:Creating custom attribute for Bold BI tenant: {TenantId}",
            tenant.Data.Id
        );
        await _boldBiNonMasterSiteAppService.CreateCustomAttributeAsync(
            args.Email, //email is used as username. it is guaranteed to be unique across the system
            _embedProperties.StandardPassword,
            tenant
        );
    }
}
