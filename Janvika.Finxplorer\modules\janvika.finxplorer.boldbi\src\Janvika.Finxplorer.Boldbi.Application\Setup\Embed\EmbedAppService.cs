using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using Janvika.Finxplorer.Boldbi.Application.Contracts.Setup;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace Janvika.Finxplorer.Boldbi.Application.Setup.Embed;

public class EmbedAppService : BoldbiAppService, IBoldBiEmbedAppService
{
    private readonly EmbedProperties _embedProperties;
    private readonly ILogger<EmbedAppService> _logger;

    public EmbedAppService(
        IOptions<EmbedProperties> embedPropertiesOptions,
        ILogger<EmbedAppService> logger
    )
    {
        _embedProperties = embedPropertiesOptions.Value;
        _logger = logger;
    }

    [AllowAnonymous]
    [HttpPost]
    public async Task<string> GetDetailsAsync(EmbedClass embedClass)
    {
        var embedQuery = embedClass.EmbedQuerString;
        embedQuery += $"&embed_user_email={CurrentUser.Email}";

        var embedDetailsUrl =
            $"/embed/authorize?{embedQuery.ToLower()}&embed_signature={GetSignatureUrl(embedQuery.ToLower())}";

        using (var client = new HttpClient())
        {
            client.BaseAddress = new Uri(embedClass.DashboardServerApiUrl);
            client.DefaultRequestHeaders.Accept.Clear();

            try
            {
                var result = await client.GetAsync(
                    embedClass.DashboardServerApiUrl + embedDetailsUrl
                );
                var responseContent = await result.Content.ReadAsStringAsync();

                if (!result.IsSuccessStatusCode)
                {
                    _logger.LogError(
                        "BoldBi:EmbedAppService:API request failed. Status: {StatusCode}, Response: {Response}",
                        result.StatusCode,
                        responseContent
                    );
                    throw new Exception(
                        $"BoldBi:EmbedAppService:API request failed with status {result.StatusCode}: {responseContent}"
                    );
                }

                // Log successful response if needed
                _logger.LogDebug(
                    "BoldBi:EmbedAppService:API response received: {Response}",
                    responseContent
                );

                return responseContent;
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(
                    ex,
                    "BoldBi:EmbedAppService:HTTP request failed for URL: {Url}",
                    embedClass.DashboardServerApiUrl + embedDetailsUrl
                );
                throw;
            }
        }
    }

    private async Task<TokenObject> GetTokenAsync()
    {
        using (var client = new HttpClient())
        {
            client.BaseAddress = new Uri(_embedProperties.ServerUrl);
            client.DefaultRequestHeaders.Accept.Clear();

            var content = new FormUrlEncodedContent(
                new[]
                {
                    new KeyValuePair<string, string>("grant_type", "password"),
                    new KeyValuePair<string, string>("Username", _embedProperties.UserEmail),
                    new KeyValuePair<string, string>("Password", _embedProperties.UserPassword),
                }
            );

            var result = await client.PostAsync(
                $"{_embedProperties.ServerUrl}/bi/api/{_embedProperties.SiteIdentifier}/token",
                content
            );
            string resultContent = await result.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<TokenObject>(resultContent);
        }
    }

    private string GetSignatureUrl(string queryString)
    {
        if (string.IsNullOrEmpty(queryString))
            return string.Empty;

        var encoding = new UTF8Encoding();
        // https://boldbirp-finxplorer-prod-0002.azurewebsites.net/ums/administration/embed-settings
        // this has to be at th UMS level
        var keyBytes = encoding.GetBytes(_embedProperties.EmbedSecret);
        var messageBytes = encoding.GetBytes(queryString);

        using (var hmacsha256 = new HMACSHA256(keyBytes))
        {
            var hashMessage = hmacsha256.ComputeHash(messageBytes);
            return Convert.ToBase64String(hashMessage);
        }
    }
}
