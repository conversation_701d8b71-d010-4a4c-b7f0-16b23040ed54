using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Janvika.Finxplorer.Boldbi.Application.Contracts.Setup;

public class DataSourceResponseDto
{
    public List<DataSourceResponseDetailDto> Data { get; set; }
    public int TotalResults { get; set; }
    public List<LinkInfo> Links { get; set; } //
}

public class DataSourceResponseDetailDto
{
    public bool CanRead { get; set; }
    public bool CanWrite { get; set; }
    public bool CanDelete { get; set; }
    public bool CanDownload { get; set; }
    public bool CanSchedule { get; set; }
    public bool CanOpen { get; set; }
    public bool CanMove { get; set; }
    public bool CanCopy { get; set; }
    public bool CanClone { get; set; }
    public bool CanCreateItem { get; set; }
    public string Id { get; set; }
    public string ItemType { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public int CreatedById { get; set; }
    public string CreatedByDisplayName { get; set; }
    public int ModifiedById { get; set; }
    public string ModifiedByFullName { get; set; }
    public string CreatedDate { get; set; }
    public string ModifiedDate { get; set; }
    public DateTime ItemModifiedDate { get; set; }
    public DateTime ItemCreatedDate { get; set; }
    public bool IsMultiDashboard { get; set; }
    public bool IsFavorite { get; set; }
    public bool IsPublic { get; set; }
    public bool IsLocked { get; set; }
    public bool IsUnlisted { get; set; }
    public string ProviderType { get; set; }
}
