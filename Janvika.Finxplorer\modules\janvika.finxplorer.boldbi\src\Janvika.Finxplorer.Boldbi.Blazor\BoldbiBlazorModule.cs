using Janvika.Finxplorer.Quiltt;
using Janvika.Finxplorer.Quiltt.Blazor;
using Janvika.Finxplorer.Boldbi.Application.Contracts;
using Janvika.Finxplorer.Boldbi.Blazor.Menus;
using Microsoft.Extensions.DependencyInjection;
using Volo.Abp.AspNetCore.Components.Web.Theming;
using Volo.Abp.AspNetCore.Components.Web.Theming.Routing;
using Volo.Abp.AspNetCore.Mvc.AntiForgery;
using Volo.Abp.AutoMapper;
using Volo.Abp.Modularity;
using Volo.Abp.UI.Navigation;

namespace Janvika.Finxplorer.Boldbi.Blazor;

[DependsOn(
    typeof(QuilttApplicationContractsModule),
    typeof(QuilttBlazorModule),
    typeof(BoldbiApplicationContractsModule),
    typeof(AbpAspNetCoreComponentsWebThemingModule),
    typeof(AbpAutoMapperModule)
)]
[DependsOn(
    typeof(QuilttApplicationModule)
)]
public class BoldbiBlazorModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        context.Services.AddAutoMapperObjectMapper<BoldbiBlazorModule>();

        Configure<AbpAutoMapperOptions>(options =>
        {
            options.AddProfile<BoldbiBlazorAutoMapperProfile>(validate: true);
        });

        Configure<AbpNavigationOptions>(options =>
        {
            options.MenuContributors.Add(new BoldbiMenuContributor());
        });

        Configure<AbpRouterOptions>(options =>
        {
            options.AdditionalAssemblies.Add(typeof(BoldbiBlazorModule).Assembly);
        });

        Configure<AbpAntiForgeryOptions>(options =>
        {
            options.AutoValidateFilter = type =>
                !type.Namespace.StartsWith("Janvika.Finxplorer.Boldbi.Application.Setup.Embed");
        });
    }
}
