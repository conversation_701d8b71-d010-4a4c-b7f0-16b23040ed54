<Project Sdk="Microsoft.NET.Sdk">
  <Import Project="..\..\common.props" />
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>Janvika.Finxplorer.Boldbi</RootNamespace>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Mvc" Version="2.2.0" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.0" />
    <PackageReference Include="Volo.Abp.AspNetCore.Mvc" Version="9.1.0" />
    <PackageReference Include="Volo.Abp.AutoMapper" Version="9.1.0" />
    <PackageReference Include="Volo.Abp.Ddd.Application" Version="9.1.0" />
    <ProjectReference Include="..\Janvika.Finxplorer.Boldbi.Application.Contracts\Janvika.Finxplorer.Boldbi.Application.Contracts.csproj" />
    <ProjectReference Include="..\Janvika.Finxplorer.Boldbi.Domain\Janvika.Finxplorer.Boldbi.Domain.csproj" />
    <ProjectReference Include="../../../janvika.finxplorer.quiltt/src/Janvika.Finxplorer.Quiltt.Application.Contracts/Janvika.Finxplorer.Quiltt.Application.Contracts.csproj" />
  </ItemGroup>
</Project>