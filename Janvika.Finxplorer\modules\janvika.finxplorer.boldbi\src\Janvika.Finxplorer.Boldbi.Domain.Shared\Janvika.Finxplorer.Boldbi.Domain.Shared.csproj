﻿<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>Janvika.Finxplorer.Boldbi</RootNamespace>
    <GenerateEmbeddedFilesManifest>true</GenerateEmbeddedFilesManifest>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Ddd.Domain.Shared" Version="9.1.0" />
    <PackageReference Include="Volo.Abp.Validation" Version="9.1.0" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.FileProviders.Embedded" Version="9.0.0.0" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Localization\Boldbi\*.json" />
    <Content Remove="Localization\Boldbi\*.json" />
  </ItemGroup>

</Project>
