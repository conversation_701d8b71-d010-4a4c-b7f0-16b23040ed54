﻿using System.Threading.Tasks;
using Janvika.Finxplorer.Boldbi.Application.Contracts.Permissions;
using Volo.Abp.UI.Navigation;

namespace Janvika.Finxplorer.Boldbi.Blazor.Menus;

public class BoldbiMenuContributor : IMenuContributor
{
    public async Task ConfigureMenuAsync(MenuConfigurationContext context)
    {
        if (context.Menu.Name == StandardMenus.Main)
        {
            context.Menu.Items.Insert(
                0,
                new ApplicationMenuItem(
                    BoldbiMenus.Prefix,
                    displayName: "Dashboard",
                    "/AccountDashboard",
                    icon: "fa fa-chart-pie", // or "fa fa-tachometer"
                    requiredPermissionName: BoldbiPermissions.Tenant.Default,
                    order: 1000
                )
            );

               context.Menu.Items.Insert(
                1,
                new ApplicationMenuItem(
                    BoldbiMenus.Prefix,
                    displayName: "Data Explorer",
                    "/DataExplorer",
                    icon: "fa fa-compass", // Changed icon for Data Explorer
                    requiredPermissionName: BoldbiPermissions.Tenant.Default,
                    order: 2000
                )
            );
        }
    }
}


/*

   context.Menu.Items.RemoveAll(x => x.Name == "Account.SecurityLogs");
            context.Menu.Items.RemoveAll(x => x.Name == "Account.LinkedAccounts");
            context.Menu.Items.RemoveAll(x => x.Name == "Account.AuthorityDelegation");


*/
