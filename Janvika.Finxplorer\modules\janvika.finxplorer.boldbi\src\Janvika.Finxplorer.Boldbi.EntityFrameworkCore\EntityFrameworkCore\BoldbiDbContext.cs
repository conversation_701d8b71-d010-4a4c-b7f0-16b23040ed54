﻿using Microsoft.EntityFrameworkCore;
using Volo.Abp.Data;
using Volo.Abp.EntityFrameworkCore;

namespace Janvika.Finxplorer.Boldbi.EntityFrameworkCore;

[ConnectionStringName(BoldbiDbProperties.ConnectionStringName)]
public class BoldbiDbContext : AbpDbContext<BoldbiDbContext>, IBoldbiDbContext
{
    /* Add DbSet for each Aggregate Root here. Example:
     * public DbSet<Question> Questions { get; set; }
     */

    public BoldbiDbContext(DbContextOptions<BoldbiDbContext> options)
        : base(options)
    {

    }

    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);

        builder.ConfigureBoldbi();
    }
}
