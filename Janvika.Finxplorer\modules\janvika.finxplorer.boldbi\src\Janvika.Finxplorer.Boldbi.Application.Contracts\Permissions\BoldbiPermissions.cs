﻿using Volo.Abp.Reflection;

namespace Janvika.Finxplorer.Boldbi.Application.Contracts.Permissions;

public class BoldbiPermissions
{
    public const string GroupName = "Boldbi";

    public static string[] GetAll()
    {
        return ReflectionHelper.GetPublicConstantsRecursively(typeof(BoldbiPermissions));
    }

    public static class Tenant
    {
        public const string Default = GroupName + ".Tenant";
    }

    public static class Host
    {
        public const string Default = GroupName + ".Host";
    }
}
