{"name": "Janvika.Finxplorer.Boldbi.Blazor", "hash": "", "contents": [{"namespace": "Janvika.Finxplorer.Boldbi.Blazor", "dependsOnModules": [{"declaringAssemblyName": "Janvika.Finxplorer.Boldbi.Application.Contracts", "namespace": "Janvika.Finxplorer.Boldbi", "name": "MyProjectApplicationContractsModule"}, {"declaringAssemblyName": "Volo.Abp.AspNetCore.Components.Web.Theming", "namespace": "Volo.Abp.AspNetCore.Components.Web.Theming", "name": "AbpAspNetCoreComponentsWebThemingModule"}, {"declaringAssemblyName": "Volo.Abp.AutoMapper", "namespace": "Volo.Abp.AutoMapper", "name": "AbpAutoMapperModule"}, {"declaringAssemblyName": "Volo.Abp.SettingManagement.Blazor", "namespace": "Volo.Abp.SettingManagement.Blazor", "name": "AbpSettingManagementBlazorModule"}], "implementingInterfaces": [{"name": "IAbpModule", "namespace": "Volo.Abp.Modularity", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.Modularity.IAbpModule"}, {"name": "IOnPreApplicationInitialization", "namespace": "Volo.Abp.Modularity", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.Modularity.IOnPreApplicationInitialization"}, {"name": "IOnApplicationInitialization", "namespace": "Volo.Abp", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.IOnApplicationInitialization"}, {"name": "IOnPostApplicationInitialization", "namespace": "Volo.Abp.Modularity", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.Modularity.IOnPostApplicationInitialization"}, {"name": "IOnApplicationShutdown", "namespace": "Volo.Abp", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.IOnApplicationShutdown"}, {"name": "IPreConfigureServices", "namespace": "Volo.Abp.Modularity", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.Modularity.IPreConfigureServices"}, {"name": "IPostConfigureServices", "namespace": "Volo.Abp.Modularity", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.Modularity.IPostConfigureServices"}], "contentType": "abpModule", "name": "BoldbiBlazorModule", "summary": null}]}