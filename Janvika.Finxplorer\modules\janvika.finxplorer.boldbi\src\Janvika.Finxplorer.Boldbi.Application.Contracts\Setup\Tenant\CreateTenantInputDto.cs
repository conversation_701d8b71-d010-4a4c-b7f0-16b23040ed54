using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Janvika.Finxplorer.Boldbi.Application.Contracts.Setup;

public class ServerConfigurationDto
{
    public DatabaseDto Database { get; set; }
    public StorageDto Storage { get; set; }
    public SiteDto Site { get; set; }
}

public class DatabaseDto
{
    public string ServerName { get; set; }
    public string DatabaseName { get; set; }
    public string UserName { get; set; }
    public string Password { get; set; }
    public bool IsNewDatabase { get; set; }
    public string MaintenanceDatabase { get; set; }
    public string Port { get; set; }
    public int ServerType { get; set; }
    public bool SslEnabled { get; set; }
    public string AdditionalParameters { get; set; }
}

public class StorageDto
{
    public int StorageType { get; set; }
    public AzureBlobDto AzureBlob { get; set; }
}

public class AzureBlobDto
{
    public string AzureBlobStorageContainerName { get; set; }
    public string AzureBlobStorageUri { get; set; }
    public string ConnectionString { get; set; }
    public string ConnectionType { get; set; }
    public string AccountName { get; set; }
    public string AccessKey { get; set; }
}

public class SiteDto
{
    public string TenantName { get; set; }
    public string TenantIdentifier { get; set; }
    public bool UseSiteIdentifier { get; set; }
    public int TenantType { get; set; }
}

public class DataStoreConfigurationDto
{
    public string ServerName { get; set; }
    public string DatabaseName { get; set; }
    public string UserName { get; set; }
    public string Password { get; set; }
    public bool IsNewDatabase { get; set; }
    public string MaintenanceDatabase { get; set; }
    public string Port { get; set; }
    public int ServerType { get; set; }
    public bool SslEnabled { get; set; }
    public string AdditionalParameters { get; set; }
}
