using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Janvika.Finxplorer.Boldbi.Application.Contracts.Setup;
using Newtonsoft.Json;

namespace Janvika.Finxplorer.Boldbi.Application.Services.Http
{
    public class HttpService<T, TC, TU, TL, TD, TR> : IHttpService<T, TC, TU, TL, TD, TR>
        where T : class
        where TC : class
        where TU : class
        where TL : class
        where TR : class
    {
        private readonly HttpClient _httpClient;
        private readonly IHttpClientFactory _httpClientFactory;
        private const string DefaultMediaType = "application/json";
        private TokenObject _currentToken;
        private readonly object _tokenLock = new object();
        private readonly TimeSpan _tokenRefreshThreshold = TimeSpan.FromMinutes(5);

        public HttpService(IHttpClientFactory httpClientFactory, string baseUrl)
        {
            _httpClientFactory =
                httpClientFactory ?? throw new ArgumentNullException(nameof(httpClientFactory));
            _httpClient = _httpClientFactory.CreateClient();
            _httpClient.BaseAddress = new Uri(baseUrl);
        }

        public async Task AuthenticateAsync(TokenRequest tokenRequest, string tokenUrl)
        {
            try
            {
                //Choose between password and client credentials grant types
                HttpContent content;
                if (tokenRequest.GrantType == "password")
                {
                    var jsonData = new
                    {
                        grant_type = tokenRequest.GrantType,
                        username = tokenRequest.Parameters["username"],
                        password = tokenRequest.Parameters["password"],
                    };
                    content = new StringContent(
                        JsonConvert.SerializeObject(jsonData),
                        Encoding.UTF8,
                        "application/json"
                    );
                }
                else
                {
                    content = new FormUrlEncodedContent(
                        new Dictionary<string, string>(tokenRequest.Parameters)
                        {
                            ["grant_type"] = tokenRequest.GrantType,
                        }
                    );
                }

                var response = await _httpClient.PostAsync(tokenUrl, content);
                await EnsureSuccessStatusCodeWithDetails(response);

                //Output the token response
                var json = await response.Content.ReadAsStringAsync();
                _currentToken = JsonConvert.DeserializeObject<TokenObject>(json);

                //Set the authorization header
                await SetAuthorizationHeaderAsync(
                    _currentToken.TokenType,
                    _currentToken.AccessToken
                );
            }
            catch (Exception ex)
            {
                throw new HttpServiceException("Failed to authenticate", ex);
            }
        }

        public async Task<T> GetIfExistsAsync(string uri)
        {
            if (_currentToken == null)
            {
                throw new HttpServiceException(
                    "Authentication required. Please call AuthenticateAsync first."
                );
            }

            await EnsureValidTokenAsync();

            if (_httpClient.DefaultRequestHeaders.Authorization == null)
            {
                await SetAuthorizationHeaderAsync(
                    _currentToken.TokenType,
                    _currentToken.AccessToken
                );
            }

            var response = await _httpClient.GetAsync(uri);

            if (
                response.StatusCode == HttpStatusCode.NotFound
                || response.StatusCode == HttpStatusCode.NoContent
            )
            {
                return null;
            }

            await EnsureSuccessStatusCodeWithDetails(response);

            var json = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<T>(json);
        }

        public async Task<T> CreateWithFormDataAsync(
            string uri,
            Dictionary<string, string> formData
        )
        {
            try
            {
                var content = new FormUrlEncodedContent(formData);
                var response = await _httpClient.PostAsync(uri, content);
                await EnsureSuccessStatusCodeWithDetails(response);

                var json = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<T>(json);
            }
            catch (Exception ex)
            {
                throw new HttpServiceException(
                    $"Error creating resource with form data at {uri}",
                    ex
                );
            }
        }

        private async Task EnsureValidTokenAsync()
        {
            if (_currentToken == null)
                throw new HttpServiceException(
                    "No authentication token available. Please authenticate first."
                );

            await RefreshTokenIfNeededAsync();
        }

        public async Task<bool> RefreshTokenIfNeededAsync()
        {
            if (_currentToken == null)
                return false;

            lock (_tokenLock)
            {
                if (_currentToken.ExpiresAt > DateTime.UtcNow.Add(_tokenRefreshThreshold))
                    return false;
            }

            // TODO: Implement your refresh token logic here
            return true;
        }

        public async Task<T> GetAsync(string uri)
        {
            if (_currentToken == null)
            {
                throw new HttpServiceException(
                    "Authentication required. Please call AuthenticateAsync first."
                );
            }

            try
            {
                await EnsureValidTokenAsync();

                if (_httpClient.DefaultRequestHeaders.Authorization == null)
                {
                    await SetAuthorizationHeaderAsync(
                        _currentToken.TokenType,
                        _currentToken.AccessToken
                    );
                }

                var response = await _httpClient.GetAsync(uri);

                if (response.StatusCode == HttpStatusCode.NoContent)
                    return null;

                await EnsureSuccessStatusCodeWithDetails(response);

                var json = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<T>(json);
            }
            catch (Exception ex)
            {
                throw new HttpServiceException($"Error getting resource from {uri}", ex);
            }
        }

        public async Task<TL> GetListAsync(string uri)
        {
            await EnsureValidTokenAsync();
            try
            {
                var response = await _httpClient.GetAsync(uri);

                if (response.StatusCode == HttpStatusCode.NoContent)
                    return null;

                await EnsureSuccessStatusCodeWithDetails(response);

                var json = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<TL>(json);
            }
            catch (Exception ex)
            {
                throw new HttpServiceException($"Error getting resource from {uri}", ex);
            }
        }

        public async Task<T> CreateAsync(string uri, TC createInputDto)
        {
            await EnsureValidTokenAsync();
            try
            {
                var content = CreateJsonContent(createInputDto);
                var response = await _httpClient.PostAsync(uri, content);
                await EnsureSuccessStatusCodeWithDetails(response);

                var json = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<T>(json);
            }
            catch (Exception ex)
            {
                throw new HttpServiceException($"Error creating resource at {uri}", ex);
            }
        }

        public async Task<TR> CreateAsyncWithStatusResponse(string uri, TC createInputDto)
        {
            await EnsureValidTokenAsync();
            try
            {
                var content = CreateJsonContent(createInputDto);
                var response = await _httpClient.PostAsync(uri, content);
                await EnsureSuccessStatusCodeWithDetails(response);

                var json = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<TR>(json);
            }
            catch (Exception ex)
            {
                throw new HttpServiceException($"Error creating resource with status at {uri}", ex);
            }
        }

        public async Task CreateManyAsync(string uri, IEnumerable<TC> createManyInputDto)
        {
            await EnsureValidTokenAsync();
            try
            {
                var content = CreateJsonContent(createManyInputDto);
                var response = await _httpClient.PostAsync(uri, content);
                await EnsureSuccessStatusCodeWithDetails(response);
            }
            catch (Exception ex)
            {
                throw new HttpServiceException($"Error creating multiple resources at {uri}", ex);
            }
        }

        public async Task<ListResultDto<T>> UpdateAsync(string uri, TU updateInputDto)
        {
            await EnsureValidTokenAsync();
            try
            {
                var content = CreateJsonContent(updateInputDto);
                var response = await _httpClient.PutAsync(uri, content);
                await EnsureSuccessStatusCodeWithDetails(response);

                var json = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<ListResultDto<T>>(json);
            }
            catch (Exception ex)
            {
                throw new HttpServiceException($"Error updating resource at {uri}", ex);
            }
        }

        public async Task DeleteAsync(string uri, TD id)
        {
            await EnsureValidTokenAsync();
            try
            {
                var response = await _httpClient.DeleteAsync($"{uri}/{id}");
                await EnsureSuccessStatusCodeWithDetails(response);
            }
            catch (Exception ex)
            {
                throw new HttpServiceException($"Error deleting resource at {uri}/{id}", ex);
            }
        }

        private async Task SetAuthorizationHeaderAsync(string tokenType, string accessToken)
        {
            if (string.IsNullOrEmpty(tokenType) || string.IsNullOrEmpty(accessToken))
            {
                throw new HttpServiceException(
                    "Token type and access token must not be null or empty"
                );
            }

            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Authorization =
                new System.Net.Http.Headers.AuthenticationHeaderValue(tokenType, accessToken);
        }

        private StringContent CreateJsonContent<TContent>(TContent content)
        {
            return new StringContent(
                JsonConvert.SerializeObject(content),
                Encoding.UTF8,
                DefaultMediaType
            );
        }

        private async Task<string> SerializeToQueryString<TQuery>(TQuery queryDto)
        {
            var dictionary = JsonConvert.DeserializeObject<Dictionary<string, string>>(
                JsonConvert.SerializeObject(queryDto)
            );

            var queryString = new StringBuilder();
            foreach (var kvp in dictionary)
            {
                if (string.IsNullOrEmpty(kvp.Value))
                    continue;

                if (queryString.Length > 0)
                    queryString.Append('&');

                queryString.Append(
                    $"{Uri.EscapeDataString(kvp.Key)}={Uri.EscapeDataString(kvp.Value)}"
                );
            }

            return queryString.ToString();
        }

        private async Task EnsureSuccessStatusCodeWithDetails(HttpResponseMessage response)
        {
            if (!response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                throw new HttpServiceException(
                    $"HTTP request failed with status code {response.StatusCode}. Response: {content}"
                );
            }
        }
    }

    public class HttpServiceException : Exception
    {
        public HttpServiceException(string message)
            : base(message) { }

        public HttpServiceException(string message, Exception innerException)
            : base(message, innerException) { }
    }

    public class ListResultDto<T>
    {
        public IList<T> Items { get; set; }
        public int TotalCount { get; set; }
    }
}
