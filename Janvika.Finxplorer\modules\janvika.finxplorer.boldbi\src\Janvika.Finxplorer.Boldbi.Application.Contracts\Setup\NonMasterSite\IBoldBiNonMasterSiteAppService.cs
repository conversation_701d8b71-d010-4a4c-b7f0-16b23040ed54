using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Janvika.Finxplorer.Boldbi.Application.Contracts.Setup;

public interface INonMasterSiteAppService
{
    public Task CreateCustomAttributeAsync(
        string Username,
        string password,
        TenantResponseDto attributeDto
    );

    // public Task<List<DashboardItem>> GetDashboardsAsync(string Username, string AbpTenantName);
}
