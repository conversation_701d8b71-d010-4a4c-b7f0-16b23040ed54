// using System;
// using System.Collections.Generic;
// using Newtonsoft.Json;

// namespace Janvika.Finxplorer.Boldbi.Application.Contracts.Setup;

// // public class PublishDashboardResultDto
// // {
// //     public bool IsNewlyPublished { get; set; }
// //     public string PublishId { get; set; }
// //     public string Status { get; set; }
// //     public DateTime? LastSynchronizedOn { get; set; }
// // }



// public class CustomAttributeResultDto
// {
//     public bool IsNewlyCreated { get; set; }
//     public int AttributeId { get; set; }
//     public bool Status { get; set; }
//     public string StatusMessage { get; set; }
// }



// // public class SynchronizationStatusDto
// // {
// //     public string LastSynchronizedStatus { get; set; }
// //     public DateTime LastSynchronizedOn { get; set; }
// // }
