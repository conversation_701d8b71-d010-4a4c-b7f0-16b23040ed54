// Updated DTOs

using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Janvika.Finxplorer.Boldbi.Application.Contracts.Setup;

// Get One Tenant

public class TenantResponseDto
{
    [JsonProperty("api_status")]
    public bool ApiStatus { get; set; }

    [JsonProperty("data")]
    public TenantData Data { get; set; }

    [JsonProperty("status")]
    public bool Status { get; set; }

    [JsonProperty("status_message")]
    public string StatusMessage { get; set; }
}

//Create Tenant

public class CreateTenantInputDto
{
    public string Email { get; set; }
    public ServerConfigurationDto ServerConfiguration { get; set; }
    public DataStoreConfigurationDto DataStoreConfiguration { get; set; }
}

// List Tenants
public class TenantListResponseDto
{
    [JsonProperty("data")]
    public List<TenantData> Data { get; set; }

    [JsonProperty("total_results")]
    public int TotalResults { get; set; }

    [JsonProperty("links")]
    public List<LinkDto> Links { get; set; }

    [JsonProperty("status_message")]
    public string StatusMessage { get; set; }
}

//APi Response
public class CreateTenantApiResponseDto
{
    [JsonProperty("api_status")]
    public bool ApiStatus { get; set; }

    [JsonProperty("data")]
    public string Data { get; set; }

    [JsonProperty("status")]
    public bool Status { get; set; }

    [JsonProperty("status_message")]
    public string StatusMessage { get; set; }
}

public class TenantDto
{
    public string TenantId { get; set; }
}

// details
