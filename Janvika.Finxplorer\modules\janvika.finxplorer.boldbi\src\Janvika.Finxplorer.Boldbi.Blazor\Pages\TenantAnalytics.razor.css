/* Add any custom styling for the button group if needed */
::deep .btn-group {
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    border-radius: 10px;
    border: 2px solid #dee2e6;
    box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1);
    transition: all 0.3s;
    cursor: pointer;
    margin: 5px;
}

/* Icons styles*/
.menu-item i {
    font-size: 24px;
    color: #495057;
    transition: color 0.3s;
}

/* Hover Effect*/
.menu-item:hover {
    background-color: #f8f9fa;
    border-color: #0d6efd;
}

/*Active (Clicked) Effect*/
.menu-item.active {
    background-color: #e9ecef;
    border: 2px solid #0d6efd;
    animation: bounce 0.3s ease-in-out;
}

.menu-item.active i {
    color: #0d6efd;
}

/*Menu Bar Styling*/
#menu-bar {
    background-color: #ffffff;
    padding: 25px 15px;
    border-radius: 10px;
    display: flex;
    justify-content: center;
    gap: 15px;
    position: relative;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/*Tooltip Styling*/
.tooltip-text {
    position: absolute;
    /*Positions tooltip below the menu item.*/
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.85);
    color: white;
    padding: 6px 10px;
    border-radius: 5px;
    font-size: 14px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
    pointer-events: none;
    margin-top: 8px;
}

/*Tooltip Arrow*/
.tooltip-text::after {
    content: "";
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border-width: 5px;
    border-style: solid;
    border-color: transparent transparent rgba(0, 0, 0, 0.85) transparent;
}

/*Show Tooltip on Hover*/
.menu-item:hover .tooltip-text {
    visibility: visible;
    /* When hovering over .menu-item, the tooltip becomes visible and fully opaque.*/
    opacity: 1;
}

#container {
    width: 13%;
    float: left;
    height: 100%;
    background: #f4f4f4;
    box-shadow: 2px 0 4px 0 rgba(0, 0, 0, .12);
    overflow: auto;
    overflow-x: hidden;
}

#grid-title {
    font-size: 17px;
    border-bottom: 1px solid #333;
    padding: 15px;
}

#panel {
    width: 100%;
    float: left;
    background: #f4f4f4;
    overflow: auto;
}

.dashboard-buttons-container {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
    margin: 5px 0;
}

.dashboard-button {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 8px 15px;
    border-radius: 6px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    min-width: 40px;
}

.dashboard-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Active button styling */
.dashboard-button.btn-success {
    transform: scale(1.05);
    box-shadow: 0 4px 10px rgba(40, 167, 69, 0.3);
}

#dashboard {
    width: 100%;
    height: calc(100vh - 200px);
    min-height: 400px;
    position: relative;
    display: block;
    overflow: auto;
    margin-top: 5px;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    transition: all 0.3s ease;
}

/* Fullscreen mode styles */
.dashboard-container.fullscreen-mode {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 9999 !important;
    background: white !important;
    border: none !important;
    border-radius: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    box-shadow: none !important;
}

/* Hide page elements when in fullscreen mode */
body.fullscreen-active .page-header,
body.fullscreen-active .dashboard-buttons-container,
body.fullscreen-active #viewer-section > *:not(.dashboard-container) {
    display: none !important;
}

/* Ensure fullscreen container takes full space */
body.fullscreen-active #viewer-section {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 9998 !important;
    background: white !important;
}

/* Ensure button text is always visible */
.dashboard-button .button-text {
    display: inline !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Fullscreen toggle button styling */
.fullscreen-toggle {
    border-left: 1px solid #dee2e6 !important;
    margin-left: 10px !important;
}

.fullscreen-toggle:hover {
    background-color: #f8f9fa !important;
    border-color: #6c757d !important;
}

/* Ensure fullscreen button text is always visible, even on mobile */
.fullscreen-toggle .button-text {
    display: inline !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Responsive design for mobile */
@media (max-width: 768px) {
    .dashboard-button .button-text {
        display: none;
    }
    
    .dashboard-button {
        min-width: 40px;
        padding: 8px;
    }
    
    .dashboard-buttons-container {
        gap: 5px;
    }
    
    /* Override mobile rule specifically for fullscreen button */
    .fullscreen-toggle .button-text {
        display: inline !important;
    }
}
