using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using Janvika.Finxplorer.Boldbi.Application;
using Janvika.Finxplorer.Boldbi.Application.Contracts.Setup;
using Janvika.Finxplorer.Boldbi.Application.Services.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Volo.Abp;

namespace Janvika.Finxplorer.BoldBI.Application.Setup;

public class DashboardAppService : BoldbiAppService, IDashboardAppService
{
    private readonly EmbedProperties _embedProperties;
    private readonly IHttpService<
        DashboardResponse,
        object,
        object,
        object,
        object,
        object
    > _dashboardHttpService;
    private readonly ILogger<DashboardAppService> _logger;

    public DashboardAppService(
        IOptions<EmbedProperties> embedPropertiesOptions,
        ILogger<DashboardAppService> logger,
        IHttpService<DashboardResponse, object, object, object, object, object> dashboardHttpService
    )
    {
        _embedProperties = embedPropertiesOptions.Value;
        _logger = logger;
        _dashboardHttpService = dashboardHttpService;
    }

    public async Task GetAdminTokenAsync()
    {
        var tokenRequest = TokenRequest.CreatePasswordGrant(
            _embedProperties.UserEmail,
            _embedProperties.UserPassword
        );

        string masterSiteTokenUrl = $"/bi/api/{_embedProperties.SiteIdentifier}/token";

        await _dashboardHttpService.AuthenticateAsync(tokenRequest, masterSiteTokenUrl);
    }

    public async Task<List<DashboardItem>> GetDashboardsAsync()
    {
        try
        {
            // Authenticate
            await GetAdminTokenAsync();

            var allDashboards = new List<DashboardItem>();
            var pageSize = 25;
            var currentPage = 1;
            var totalPages = 1;

            do
            {
                var dashboardResponse = await _dashboardHttpService.GetAsync(
                    $"/bi/api/{_embedProperties.SiteIdentifier}/v5.0/dashboards?page={currentPage}&page_size={pageSize}"
                );

                if (dashboardResponse?.Data == null)
                {
                    _logger.LogWarning("No dashboards found on page {Page}", currentPage);
                    break;
                }

                allDashboards.AddRange(dashboardResponse.Data);

                totalPages = (int)Math.Ceiling(dashboardResponse.TotalResults / (double)pageSize);

                if (currentPage >= totalPages)
                {
                    _logger.LogInformation("Retrieved all {Pages} pages of dashboards", totalPages);
                    break;
                }

                currentPage++;
            } while (true);

            if (allDashboards.Any())
            {
                return allDashboards;
            }

            throw new UserFriendlyException("No dashboards found.");
        }
        catch (HttpServiceException ex)
        {
            throw new UserFriendlyException($"Failed to get dashboards: {ex.Message}");
        }
    }
}
