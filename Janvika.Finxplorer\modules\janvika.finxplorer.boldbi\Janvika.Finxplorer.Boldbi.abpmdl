{"folders": {"items": {"src": {}, "test": {}}}, "packages": {"Janvika.Finxplorer.Boldbi.Domain": {"path": "src/Janvika.Finxplorer.Boldbi.Domain/Janvika.Finxplorer.Boldbi.Domain.abppkg", "folder": "src"}, "Janvika.Finxplorer.Boldbi.Application.Contracts": {"path": "src/Janvika.Finxplorer.Boldbi.Application.Contracts/Janvika.Finxplorer.Boldbi.Application.Contracts.abppkg", "folder": "src"}, "Janvika.Finxplorer.Boldbi.Application": {"path": "src/Janvika.Finxplorer.Boldbi.Application/Janvika.Finxplorer.Boldbi.Application.abppkg", "folder": "src"}, "Janvika.Finxplorer.Boldbi.EntityFrameworkCore": {"path": "src/Janvika.Finxplorer.Boldbi.EntityFrameworkCore/Janvika.Finxplorer.Boldbi.EntityFrameworkCore.abppkg", "folder": "src"}, "Janvika.Finxplorer.Boldbi.Blazor": {"path": "src/Janvika.Finxplorer.Boldbi.Blazor/Janvika.Finxplorer.Boldbi.Blazor.abppkg", "folder": "src"}, "Janvika.Finxplorer.Boldbi.Blazor.Server": {"path": "src/Janvika.Finxplorer.Boldbi.Blazor.Server/Janvika.Finxplorer.Boldbi.Blazor.Server.abppkg", "folder": "src"}, "Janvika.Finxplorer.Boldbi.TestBase": {"path": "test/Janvika.Finxplorer.Boldbi.TestBase/Janvika.Finxplorer.Boldbi.TestBase.abppkg", "folder": "test"}, "Janvika.Finxplorer.Boldbi.EntityFrameworkCore.Tests": {"path": "test/Janvika.Finxplorer.Boldbi.EntityFrameworkCore.Tests/Janvika.Finxplorer.Boldbi.EntityFrameworkCore.Tests.abppkg", "folder": "test"}, "Janvika.Finxplorer.Boldbi.Domain.Tests": {"path": "test/Janvika.Finxplorer.Boldbi.Domain.Tests/Janvika.Finxplorer.Boldbi.Domain.Tests.abppkg", "folder": "test"}, "Janvika.Finxplorer.Boldbi.Application.Tests": {"path": "test/Janvika.Finxplorer.Boldbi.Application.Tests/Janvika.Finxplorer.Boldbi.Application.Tests.abppkg", "folder": "test"}, "Janvika.Finxplorer.Boldbi.Domain.Shared": {"path": "src/Janvika.Finxplorer.Boldbi.Domain.Shared/Janvika.Finxplorer.Boldbi.Domain.Shared.abppkg", "folder": "src"}}, "imports": {"Janvika.Finxplorer.Quiltt": {"path": "../janvika.finxplorer.quiltt/Janvika.Finxplorer.Quiltt.abpmdl"}}}