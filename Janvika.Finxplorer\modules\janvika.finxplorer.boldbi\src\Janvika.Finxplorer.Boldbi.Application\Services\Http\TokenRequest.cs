using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace Janvika.Finxplorer.Boldbi.Application.Services.Http;

public class TokenRequest
{
    public string GrantType { get; set; }
    public Dictionary<string, string> Parameters { get; } = new Dictionary<string, string>();

    public static TokenRequest CreatePasswordGrant(string username, string password)
    {
        var request = new TokenRequest { GrantType = "password" };
        request.Parameters.Add("username", username);
        request.Parameters.Add("password", password);
        return request;
    }

    public static TokenRequest CreateClientCredentialsGrant(string clientId, string clientSecret)
    {
        var request = new TokenRequest { GrantType = "client_credentials" };
        request.Parameters.Add("client_id", clientId);
        request.Parameters.Add("client_secret", clientSecret);
        return request;
    }
}
