@page "/AccountDashboard"

@attribute [Authorize(BoldbiPermissions.Tenant.Default)]
@using Janvika.Finxplorer.Boldbi.Application.Contracts.Permissions
@using Janvika.Finxplorer.Boldbi.Application.Contracts.Setup
@using Janvika.Finxplorer.Quiltt.AccountDetail 
@using Janvika.Finxplorer.Quiltt.Localization
@using Microsoft.AspNetCore.Authorization
@using Volo.Abp.AspNetCore.Components.Web.Theming.Layout
@inject AbpBlazorMessageLocalizerHelper<QuilttResource> LH
@using Blazorise;

@inherits BoldbiComponentBase

@* ************************* PAGE HEADER ************************* *@
<PageHeader BreadcrumbItems="@BreadcrumbItems" />

<div id="viewer-section" style="width: 100%;">

    <div class="btn-group dashboard-buttons-container d-flex justify-content-center" role="group" aria-label="Dashboard selection">
        @foreach (var dashboard in Dashboards)
        {
            <Button Color="@(CurrentDashboardId == dashboard.Id ? Color.Primary : Color.Default)"
                    Clicked="@(() => RenderSelectedDashboard(dashboard.Id))"
                    Title="@dashboard.Name"
                    Class="@($"btn-outline-primary dashboard-button {(CurrentDashboardId == dashboard.Id ? "active" : "")}")">
                <Icon Name="@GetFontAwesomeIconName(dashboard.IconClass)" />
                <span class="button-text ms-2">@dashboard.Name</span>
            </Button>
        }
        
        @* Fullscreen Toggle Button *@
        <Button Color="Color.Secondary"
                Clicked="@ToggleFullscreen"
                Title="Enter Fullscreen"
                Class="btn-outline-secondary dashboard-button fullscreen-toggle"
                Style="position: relative; background-color: #6c757d !important; border-color: #6c757d !important;">
            <Icon Name="@(IsFullscreen ? "fas fa-compress" : "fas fa-expand")" style="color: #ffffff !important;" />
            <span class="button-text ms-2" style="display: inline !important; opacity: 1 !important; color: #ffffff !important; font-weight: 500 !important; text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;">
                @(IsFullscreen ? "Exit" : "Fullscreen")
            </span>
        </Button>
    </div>
  
    <div id="dashboard" class="dashboard-container @(IsFullscreen ? "fullscreen-mode" : "")" style="position: relative;">
        <!-- The dashboard container -->
        @if (IsDashboardSwitching)
        {
            <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: rgba(255,255,255,0.7); display: flex; align-items: center; justify-content: center; z-index: 10;">
                <Spinner Color="Color.Primary" Size="SpinnerSize.Large" />
            </div>
        }
    </div>
</div>

@if (IsLoading)
{
    <div class="loading-container" style="display: flex; flex-direction: column; justify-content: center; align-items: center; min-height: 400px; padding: 2rem;">
        <div class="loading-steps">
            <div class="step @(LoadingStep >= 1 ? "completed" : "active")" style="display: flex; align-items: center; margin-bottom: 1rem; color: @(LoadingStep >= 1 ? "#28a745" : "#007bff");">
                <Icon Name="@(LoadingStep >= 1 ? "fas fa-check-circle" : "fas fa-circle-notch fa-spin")" style="margin-right: 0.5rem;" />
                <span>Loading dashboard configurations...</span>
            </div>
            <div class="step @(LoadingStep >= 2 ? "completed" : LoadingStep == 2 ? "active" : "")" style="display: flex; align-items: center; margin-bottom: 1rem; color: @(LoadingStep >= 2 ? "#28a745" : LoadingStep == 2 ? "#007bff" : "#6c757d");">
                <Icon Name="@(LoadingStep >= 2 ? "fas fa-check-circle" : LoadingStep == 2 ? "fas fa-circle-notch fa-spin" : "fas fa-circle")" style="margin-right: 0.5rem;" />
                <span>Initializing BoldBI SDK...</span>
            </div>
            <div class="step @(LoadingStep >= 3 ? "completed" : LoadingStep == 3 ? "active" : "")" style="display: flex; align-items: center; margin-bottom: 1rem; color: @(LoadingStep >= 3 ? "#28a745" : LoadingStep == 3 ? "#007bff" : "#6c757d");">
                <Icon Name="@(LoadingStep >= 3 ? "fas fa-check-circle" : LoadingStep == 3 ? "fas fa-circle-notch fa-spin" : "fas fa-circle")" style="margin-right: 0.5rem;" />
                <span>Rendering dashboard...</span>
            </div>
        </div>
        <div class="progress" style="width: 300px; margin-top: 1rem;">
            <div class="progress-bar" role="progressbar" style="width: @(LoadingStep * 33.33)%" aria-valuenow="@(LoadingStep * 33.33)" aria-valuemin="0" aria-valuemax="100"></div>
        </div>
    </div>
}
else
{
    <!-- Your existing dashboard content -->
}
