using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using Janvika.Finxplorer.Boldbi.Application.Contracts.Setup;
using Janvika.Finxplorer.Boldbi.Application.Services.Http;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Volo.Abp;

namespace Janvika.Finxplorer.Boldbi.Application.Setup;

public class NonMasterSiteAppService : BoldbiAppService, INonMasterSiteAppService
{
    private readonly EmbedProperties _embedProperties;

    private readonly IHttpService<
        CustomAttributeResponseDto,
        object,
        UpdateCustomAttributeDto,
        object,
        object,
        CreateCustomAttributeApiResponseDto
    > _NonMasterSiteAppService;

    public NonMasterSiteAppService(
        IOptions<EmbedProperties> embedPropertiesOptions,
        IHttpService<
            CustomAttributeResponseDto,
            object,
            UpdateCustomAttributeDto,
            object,
            object,
            CreateCustomAttributeApiResponseDto
        > httpService
    )
    {
        _embedProperties = embedPropertiesOptions.Value;
        _NonMasterSiteAppService = httpService;
    }

    public async Task CreateCustomAttributeAsync(
        string Username,
        string password,
        TenantResponseDto tenant
    )
    {
        var universalTenantName = tenant.Data.TenantName; //This is the Bold BI tenant name and the ABP tenant name

        var createAttributeDto = new CreateCustomAttributeDto
        {
            Name = "finxplorer-tenant-connectionstring",
            Value = JsonConvert.SerializeObject(
                new
                {
                    ServerName = _embedProperties.DatabaseServerName,
                    Database = universalTenantName,
                    UserName = _embedProperties.DatabaseUserName,
                    Password = _embedProperties.DatabasePassword,
                    Port = "",
                    SslMode = "",
                    TrustServerCertificate = "",
                    AdvancedSettings = "",
                    CommandTimeout = "",
                    Schema = "",
                }
            ),
            Description = "finxplorer-tenant-connectionstring",
            CanEncrypt = true,
            UserId = 1,
        };

        try
        {
            // Authenticate first
            var tokenRequest = TokenRequest.CreatePasswordGrant(Username, password);
            string tokenUrl = $"/bi/api/site/{universalTenantName}/token";
            await _NonMasterSiteAppService.AuthenticateAsync(tokenRequest, tokenUrl);

            // Use GetIfExistsAsync so that if the resource is not found, we receive null (instead of an exception)
            string attributeUrl =
                $"/bi/api/site/{tenant.Data.TenantName}/v5.0/attribute/user/{createAttributeDto.UserId}";
            var existingAttributes = await _NonMasterSiteAppService.GetIfExistsAsync(attributeUrl);

            if (existingAttributes?.Data == null)
            {
                // No attributes found, so create a new custom attribute
                await CreateNewAttributeAsync(universalTenantName, createAttributeDto);
            }
            else
            {
                var existingAttribute = existingAttributes.Data.FirstOrDefault(attr =>
                    attr.Name == createAttributeDto.Name
                );

                if (existingAttribute == null)
                {
                    // Attribute not found in the list; create it
                    await CreateNewAttributeAsync(universalTenantName, createAttributeDto);
                }
                else
                {
                    // Update the existing attribute
                    await UpdateExistingAttributeAsync(
                        universalTenantName,
                        existingAttribute.Id,
                        createAttributeDto
                    );
                }
            }
        }
        catch (HttpServiceException ex)
        {
            throw new UserFriendlyException(
                "Failed to create or update custom attribute. " + ex.Message
            );
        }
    }

    private async Task CreateNewAttributeAsync(
        string boldBiTenant,
        CreateCustomAttributeDto attributeDto
    )
    {
        var url = $"/bi/api/site/{boldBiTenant}/v5.0/attribute/user";
        await _NonMasterSiteAppService.CreateAsyncWithStatusResponse(url, attributeDto);
    }

    private async Task UpdateExistingAttributeAsync(
        string boldBiTenant,
        int attributeId,
        CreateCustomAttributeDto attributeDto
    )
    {
        var updateDto = new UpdateCustomAttributeDto
        {
            Id = attributeId,
            Name = attributeDto.Name,
            Value = attributeDto.Value,
            Description = attributeDto.Description,
            CanEncrypt = attributeDto.CanEncrypt,
            UserId = attributeDto.UserId,
        };

        string updateUrl = $"/bi/api/site/{boldBiTenant}/v5.0/attribute/user";
        await _NonMasterSiteAppService.UpdateAsync(updateUrl, updateDto);
    }
}
