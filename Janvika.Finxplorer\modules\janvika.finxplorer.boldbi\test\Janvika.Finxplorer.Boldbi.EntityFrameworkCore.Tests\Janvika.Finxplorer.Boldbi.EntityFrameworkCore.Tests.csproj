﻿<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>Janvika.Finxplorer.Boldbi</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.11.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Proxies" Version="9.0.0.0" />
    <ProjectReference Include="..\..\src\Janvika.Finxplorer.Boldbi.EntityFrameworkCore\Janvika.Finxplorer.Boldbi.EntityFrameworkCore.csproj" />
    <ProjectReference Include="..\Janvika.Finxplorer.Boldbi.Application.Tests\Janvika.Finxplorer.Boldbi.Application.Tests.csproj" />
    <PackageReference Include="Volo.Abp.EntityFrameworkCore.Sqlite" Version="9.1.0" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="EntityFrameworkCore\Applications\" />
    <Folder Include="EntityFrameworkCore\Domains\" />
  </ItemGroup>

</Project>
