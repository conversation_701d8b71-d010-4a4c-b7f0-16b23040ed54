<Project Sdk="Microsoft.NET.Sdk">
  <Import Project="..\..\common.props" />
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>Janvika.Finxplorer.Boldbi</RootNamespace>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Volo.Abp.Ddd.Application.Contracts" Version="9.1.0" />
    <PackageReference Include="Volo.Abp.Authorization" Version="9.1.0" />
    <ProjectReference Include="..\Janvika.Finxplorer.Boldbi.Domain.Shared\Janvika.Finxplorer.Boldbi.Domain.Shared.csproj" />
    <ProjectReference Include="../../../janvika.finxplorer.quiltt/src/Janvika.Finxplorer.Quiltt.Application.Contracts/Janvika.Finxplorer.Quiltt.Application.Contracts.csproj" />
  </ItemGroup>
</Project>